{"pdf_info": [{"para_blocks": [{"bbox": [232, 192, 728, 249], "type": "title", "lines": [{"bbox": [232, 192, 728, 249], "spans": [{"bbox": [232, 192, 728, 249], "type": "text", "content": "AI巡考场景化解决方案"}]}], "index": 0, "level": 1}, {"bbox": [372, 393, 588, 442], "type": "text", "lines": [{"bbox": [372, 393, 588, 442], "spans": [{"bbox": [372, 393, 588, 442], "type": "text", "content": "云能力中心 成都产业研究院  2024年10月"}]}], "index": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 0}, {"para_blocks": [{"type": "image", "bbox": [271, 135, 681, 449], "blocks": [{"bbox": [271, 135, 681, 449], "lines": [{"bbox": [271, 135, 681, 449], "spans": [{"bbox": [271, 135, 681, 449], "type": "image", "image_path": "2c328ae21230bff7495a3b7bac2ed5e82c78b86b9a56d52b1c7c3b89ab1d3b42.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 1}, {"para_blocks": [{"bbox": [31, 29, 683, 60], "type": "title", "lines": [{"bbox": [31, 29, 683, 60], "spans": [{"bbox": [31, 29, 683, 60], "type": "text", "content": "政策背景 | 国家引导、省侧需要，AI+助力巡考工作转型升级"}]}], "index": 0, "level": 1}, {"bbox": [29, 82, 921, 122], "type": "text", "lines": [{"bbox": [29, 82, 921, 122], "spans": [{"bbox": [29, 82, 921, 122], "type": "text", "content": "国家政策引导要求24年高考全量覆盖AI+巡考，时间紧、任务重，广东、山东、新疆、安徽等多省考试院提出要更进一步明确和优化AI+巡考的算力资源、算法能力、工作机制，加快推进助力巡考转型升级。"}]}], "index": 1}, {"bbox": [122, 142, 381, 164], "type": "title", "lines": [{"bbox": [122, 142, 381, 164], "spans": [{"bbox": [122, 142, 381, 164], "type": "text", "content": "教育部考试院加速推进AI+巡考应用"}]}], "index": 2, "level": 1}, {"bbox": [590, 142, 834, 164], "type": "title", "lines": [{"bbox": [590, 142, 834, 164], "spans": [{"bbox": [590, 142, 834, 164], "type": "text", "content": "各省考试院提出AI+巡考发展诉求"}]}], "index": 3, "level": 1}, {"bbox": [72, 220, 182, 255], "type": "text", "lines": [{"bbox": [72, 220, 182, 255], "spans": [{"bbox": [72, 220, 182, 255], "type": "text", "content": "教育部局函件"}]}], "index": 4}, {"bbox": [254, 191, 449, 213], "type": "title", "lines": [{"bbox": [254, 191, 449, 213], "spans": [{"bbox": [254, 191, 449, 213], "type": "text", "content": "国家政策推动AI+巡考发展"}]}], "index": 5, "level": 1}, {"bbox": [232, 229, 461, 291], "type": "text", "lines": [{"bbox": [232, 229, 461, 291], "spans": [{"bbox": [232, 229, 461, 291], "type": "text", "content": "2023年试点：前期试点北京、辽宁、福建、河南、广西、重庆、四川7省高考与24省研考，试点有成效、推广有基础"}]}], "index": 6}, {"bbox": [232, 307, 463, 389], "type": "text", "lines": [{"bbox": [232, 307, 463, 389], "spans": [{"bbox": [232, 307, 463, 389], "type": "text", "content": "2024年应用：正式发文，推进2024年高考考点全面覆盖人工智能巡查，共计约8000考点、40万考场，并于高考安全全中集中部署，推进AI+巡考应用推广"}]}], "index": 7}, {"bbox": [564, 187, 640, 205], "type": "title", "lines": [{"bbox": [564, 187, 640, 205], "spans": [{"bbox": [564, 187, 640, 205], "type": "text", "content": "广东考试院"}]}], "index": 8, "level": 1}, {"bbox": [513, 222, 674, 284], "type": "text", "lines": [{"bbox": [513, 222, 674, 284], "spans": [{"bbox": [513, 222, 674, 284], "type": "text", "content": "如何规模化、体系化覆盖明确业务处室与信息处分工符合政令要求，落地可执行"}]}], "index": 9}, {"bbox": [564, 307, 640, 326], "type": "title", "lines": [{"bbox": [564, 307, 640, 326], "spans": [{"bbox": [564, 307, 640, 326], "type": "text", "content": "新疆考试院"}]}], "index": 10, "level": 1}, {"bbox": [513, 341, 686, 403], "type": "text", "lines": [{"bbox": [513, 341, 686, 403], "spans": [{"bbox": [513, 341, 686, 403], "type": "text", "content": "解决群体作弊防控、考务人员疏漏、试卷保密巡察问题部分巡考设备陈旧，高效利旧"}]}], "index": 11}, {"bbox": [782, 187, 858, 205], "type": "title", "lines": [{"bbox": [782, 187, 858, 205], "spans": [{"bbox": [782, 187, 858, 205], "type": "text", "content": "山东考试院"}]}], "index": 12, "level": 1}, {"bbox": [729, 222, 902, 284], "type": "text", "lines": [{"bbox": [729, 222, 902, 284], "spans": [{"bbox": [729, 222, 902, 284], "type": "text", "content": "时间紧、任务重、预算有限，需探索云化部署，试点先行建立预警、反馈、处理机制"}]}], "index": 13}, {"bbox": [782, 307, 858, 326], "type": "title", "lines": [{"bbox": [782, 307, 858, 326], "spans": [{"bbox": [782, 307, 858, 326], "type": "text", "content": "安徽考试院"}]}], "index": 14, "level": 1}, {"bbox": [729, 341, 906, 403], "type": "text", "lines": [{"bbox": [729, 341, 906, 403], "spans": [{"bbox": [729, 341, 906, 403], "type": "text", "content": "算法与算力是两大核心，保障AI全识别、提高准度，保障考试期间算力、服务器资源"}]}], "index": 15}, {"type": "image", "bbox": [69, 220, 184, 386], "blocks": [{"bbox": [69, 220, 184, 386], "lines": [{"bbox": [69, 220, 184, 386], "spans": [{"bbox": [69, 220, 184, 386], "type": "image", "image_path": "f5d9f5965f47b8b8c1109406ebd5d9a9921c11f33b3b7e92059ae064febf0580.jpg"}]}], "index": 16, "type": "image_body"}], "index": 16}, {"bbox": [36, 463, 61, 505], "type": "title", "lines": [{"bbox": [36, 463, 61, 505], "spans": [{"bbox": [36, 463, 61, 505], "type": "text", "content": "小结"}]}], "index": 17, "level": 1}, {"bbox": [95, 463, 816, 510], "type": "text", "lines": [{"bbox": [95, 463, 816, 510], "spans": [{"bbox": [95, 463, 816, 510], "type": "text", "content": "1、算力资源：基于移动云打造云化部署方案，全面保障AI+巡考的算力、网络、安全相关资源满足考试需求。2、算法能力：进一步优化AI+算法，保障疑似作弊行为能够全识别、高准确，降低人工审核及一线考务的工作负担。"}]}], "index": 18}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 2}, {"para_blocks": [{"bbox": [33, 25, 421, 56], "type": "title", "lines": [{"bbox": [33, 25, 421, 56], "spans": [{"bbox": [33, 25, 421, 56], "type": "text", "content": "市场规模 | AI巡考业务市场空间巨大"}]}], "index": 0, "level": 1}, {"bbox": [40, 84, 765, 105], "type": "text", "lines": [{"bbox": [40, 84, 765, 105], "spans": [{"bbox": [40, 84, 765, 105], "type": "text", "content": "AI巡考业务市场规模大，市场成熟期预计每年市场规模超百亿，基于移动云AI底座能力在教育考试领域形成技术卡位。"}]}], "index": 1}, {"bbox": [201, 122, 285, 142], "type": "title", "lines": [{"bbox": [201, 122, 285, 142], "spans": [{"bbox": [201, 122, 285, 142], "type": "text", "content": "市场规模大"}]}], "index": 2, "level": 1}, {"bbox": [69, 153, 413, 192], "type": "text", "lines": [{"bbox": [69, 153, 413, 192], "spans": [{"bbox": [69, 153, 413, 192], "type": "text", "content": "目前全国各省标准化考场的AI巡考大多数处于试点阶段，全国超8000标准化考点，超40万间标准化考场，市场空间大"}]}], "index": 3}, {"bbox": [84, 205, 223, 259], "type": "text", "lines": [{"bbox": [84, 205, 223, 259], "spans": [{"bbox": [84, 205, 223, 259], "type": "text", "content": "2023年全国高考人数1291万人市场规模15亿+"}]}], "index": 4}, {"bbox": [273, 205, 410, 259], "type": "text", "lines": [{"bbox": [273, 205, 410, 259], "spans": [{"bbox": [273, 205, 410, 259], "type": "text", "content": "2023年全国研考人数大约474万人市场规模3亿+"}]}], "index": 5}, {"bbox": [74, 295, 229, 348], "type": "text", "lines": [{"bbox": [74, 295, 229, 348], "spans": [{"bbox": [74, 295, 229, 348], "type": "text", "content": "2023年全国中考人数大约1540万人市场规模15亿+"}]}], "index": 6}, {"bbox": [264, 295, 417, 348], "type": "text", "lines": [{"bbox": [264, 295, 417, 348], "spans": [{"bbox": [264, 295, 417, 348], "type": "text", "content": "2023年全国四六级人数大约2340万人市场规模30亿+"}]}], "index": 7}, {"bbox": [71, 384, 427, 424], "type": "text", "lines": [{"bbox": [71, 384, 427, 424], "spans": [{"bbox": [71, 384, 427, 424], "type": "text", "content": "除教育类外，职业资格、社会及企业招聘类超过千万人次的纸笔均有建设需求，考试市场规模合计约百亿，考试场景应用广泛。"}]}], "index": 8}, {"bbox": [66, 452, 424, 494], "type": "text", "lines": [{"bbox": [66, 452, 424, 494], "spans": [{"bbox": [66, 452, 424, 494], "type": "text", "content": "国家公务员等选 职业资格证书考试 其他各类考试拔性考试 （人社局/工信部） （招聘类）"}]}], "index": 9}, {"bbox": [660, 123, 746, 144], "type": "title", "lines": [{"bbox": [660, 123, 746, 144], "spans": [{"bbox": [660, 123, 746, 144], "type": "text", "content": "技术卡位高"}]}], "index": 10, "level": 1}, {"bbox": [522, 156, 901, 217], "type": "text", "lines": [{"bbox": [522, 156, 901, 217], "spans": [{"bbox": [522, 156, 901, 217], "type": "text", "content": "AI视觉产业链涵盖了多个层面和环节，涉及到硬件制造、算法开发、数据采集和处理、应用开发以及服务提供等多个领域，行业空间大，市场规模持续增长。"}]}], "index": 11}, {"bbox": [522, 222, 886, 261], "type": "text", "lines": [{"bbox": [522, 222, 886, 261], "spans": [{"bbox": [522, 222, 886, 261], "type": "text", "content": "教育是AI视觉技术应用的重要领域，大量教育数据将促进自有AI模型完善，打造AI卡位能力。"}]}], "index": 12}, {"bbox": [533, 288, 879, 307], "type": "title", "lines": [{"bbox": [533, 288, 879, 307], "spans": [{"bbox": [533, 288, 879, 307], "type": "text", "content": "上游-基础层 中游-技术层 下游-应用层"}]}], "index": 13, "level": 1}, {"bbox": [506, 332, 895, 366], "type": "text", "lines": [{"bbox": [506, 332, 895, 366], "spans": [{"bbox": [506, 332, 895, 366], "type": "text", "content": "- 2023年中国教育考试信息化行业市场规模同比增长 "}, {"bbox": [506, 332, 895, 366], "type": "inline_equation", "content": "7\\%"}, {"bbox": [506, 332, 895, 366], "type": "text", "content": " ，预计2024年中国教育考试信息化行业市场规模同比增长 "}, {"bbox": [506, 332, 895, 366], "type": "inline_equation", "content": "9.2\\%"}]}], "index": 14}, {"type": "image", "bbox": [570, 403, 834, 522], "blocks": [{"bbox": [570, 403, 834, 522], "lines": [{"bbox": [570, 403, 834, 522], "spans": [{"bbox": [570, 403, 834, 522], "type": "image", "image_path": "0f4a5ca74549f95ae3e9efae83ad1cbb741fa455bc51efbbae501d6c79af8089.jpg"}]}], "index": 15, "type": "image_body"}, {"bbox": [600, 386, 814, 398], "lines": [{"bbox": [600, 386, 814, 398], "spans": [{"bbox": [600, 386, 814, 398], "type": "text", "content": "2019-2024年中国教育考试信息化行业市场规模及增速"}]}], "index": 16, "type": "image_caption"}], "index": 15}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 3}, {"para_blocks": [{"bbox": [42, 25, 528, 57], "type": "title", "lines": [{"bbox": [42, 25, 528, 57], "spans": [{"bbox": [42, 25, 528, 57], "type": "text", "content": "我司优势 | 成熟巡考产品，项目落地经验丰富"}]}], "index": 0, "level": 1}, {"bbox": [21, 79, 943, 122], "type": "text", "lines": [{"bbox": [21, 79, 943, 122], "spans": [{"bbox": [21, 79, 943, 122], "type": "text", "content": "- 2024年中国移动积极落实教育部AI巡查巡检文件要求，组建技术+支撑专班，一个月时间内完成云化服务技术方案打造及试点验证，支撑四川、广东、湖南等18省落地服务24年高考，打造多个全省覆盖标杆，形成了良好的社会效益，以人工智能+考试打造教育领域新质生产力。"}]}], "index": 1}, {"bbox": [149, 173, 182, 192], "type": "title", "lines": [{"bbox": [149, 173, 182, 192], "spans": [{"bbox": [149, 173, 182, 192], "type": "text", "content": "四川"}]}], "index": 2, "level": 1}, {"bbox": [378, 142, 581, 162], "type": "title", "lines": [{"bbox": [378, 142, 581, 162], "spans": [{"bbox": [378, 142, 581, 162], "type": "text", "content": "AI巡检：打造多个全省覆盖标杆"}]}], "index": 3, "level": 1}, {"bbox": [463, 176, 498, 192], "type": "title", "lines": [{"bbox": [463, 176, 498, 192], "spans": [{"bbox": [463, 176, 498, 192], "type": "text", "content": "广东"}]}], "index": 4, "level": 1}, {"bbox": [775, 177, 808, 192], "type": "title", "lines": [{"bbox": [775, 177, 808, 192], "spans": [{"bbox": [775, 177, 808, 192], "type": "text", "content": "湖南"}]}], "index": 5, "level": 1}, {"bbox": [70, 201, 263, 240], "type": "text", "lines": [{"bbox": [70, 201, 263, 240], "spans": [{"bbox": [70, 201, 263, 240], "type": "text", "content": "全省21市州、176区县共计270个考点、194个保密室覆盖"}]}], "index": 6}, {"bbox": [40, 245, 295, 369], "type": "text", "lines": [{"bbox": [40, 245, 295, 369], "spans": [{"bbox": [40, 245, 295, 369], "type": "text", "content": "- 标杆效益：国家考试院全国试点中唯一实现省级全量点位覆盖的标杆，同时也是全国试点中唯一完全实现云化部署的落地案例- 客户好评：获得省领导、省教育厅高度评价- 社会效益：39家媒体报道，其中包括人民日报、人民邮电报、新华网、今日头条"}]}], "index": 7}, {"bbox": [391, 201, 568, 240], "type": "text", "lines": [{"bbox": [391, 201, 568, 240], "spans": [{"bbox": [391, 201, 568, 240], "type": "text", "content": "全省21个市州，386个高考考点1.8万考场、51万余名高考考生"}]}], "index": 8}, {"bbox": [352, 246, 605, 369], "type": "text", "lines": [{"bbox": [352, 246, 605, 369], "spans": [{"bbox": [352, 246, 605, 369], "type": "text", "content": "- 标杆效益：广东首次进行大规模云化部署应用在高考，过去试点期间均采用本地部署- 客户好评：获得省考试院高度赞赏，指出“完成了不可能完成的任务”- 边际效益：定义“AI巡检元年”，后续推广应用于自考、研究生考试、其他社会考试"}]}], "index": 9}, {"bbox": [710, 201, 873, 219], "type": "text", "lines": [{"bbox": [710, 201, 873, 219], "spans": [{"bbox": [710, 201, 873, 219], "type": "text", "content": "全省14个市州，121个保密室"}]}], "index": 10}, {"bbox": [738, 224, 849, 240], "type": "title", "lines": [{"bbox": [738, 224, 849, 240], "spans": [{"bbox": [738, 224, 849, 240], "type": "text", "content": "242个摄像头全覆盖"}]}], "index": 11, "level": 1}, {"bbox": [662, 246, 919, 284], "type": "text", "lines": [{"bbox": [662, 246, 919, 284], "spans": [{"bbox": [662, 246, 919, 284], "type": "text", "content": "- 标杆效益：湖南省首次实现在高考中的试卷保密室全面云化部署落地"}]}], "index": 12}, {"bbox": [662, 288, 926, 369], "type": "text", "lines": [{"bbox": [662, 288, 926, 369], "spans": [{"bbox": [662, 288, 926, 369], "type": "text", "content": "- 客户好评：获得省领导、省考试院的一致认可- 边际效益：云+专线的创新模式在考试业务中的应用得到认可，未来升级规划全新架构的考务平台+考务专线建设方案"}]}], "index": 13}, {"bbox": [359, 387, 602, 406], "type": "title", "lines": [{"bbox": [359, 387, 602, 406], "spans": [{"bbox": [359, 387, 602, 406], "type": "text", "content": "试点标杆：多位省级领导现场视察指导"}]}], "index": 14, "level": 1}, {"type": "image", "bbox": [44, 414, 205, 486], "blocks": [{"bbox": [44, 414, 205, 486], "lines": [{"bbox": [44, 414, 205, 486], "spans": [{"bbox": [44, 414, 205, 486], "type": "image", "image_path": "645609ae6d5629d0fbedf1f6be17ae61f77285d2fcf55e96a07b8f70e1e18982.jpg"}]}], "index": 15, "type": "image_body"}, {"bbox": [55, 494, 192, 525], "lines": [{"bbox": [55, 494, 192, 525], "spans": [{"bbox": [55, 494, 192, 525], "type": "text", "content": "云南省委副书记、省长 王予波昆明十中考点"}]}], "index": 16, "type": "image_caption"}], "index": 15}, {"type": "image", "bbox": [232, 414, 369, 486], "blocks": [{"bbox": [232, 414, 369, 486], "lines": [{"bbox": [232, 414, 369, 486], "spans": [{"bbox": [232, 414, 369, 486], "type": "image", "image_path": "b984f96c8cf96d8caffe0cb77d01f7f7ab16b475f22350453c76668fce0ceb17.jpg"}]}], "index": 17, "type": "image_body"}, {"bbox": [237, 494, 369, 525], "lines": [{"bbox": [237, 494, 369, 525], "spans": [{"bbox": [237, 494, 369, 525], "type": "text", "content": "安徽省委副书记、省长 王清宪合肥一中考点"}]}], "index": 18, "type": "image_caption"}], "index": 17}, {"type": "image", "bbox": [405, 414, 554, 486], "blocks": [{"bbox": [405, 414, 554, 486], "lines": [{"bbox": [405, 414, 554, 486], "spans": [{"bbox": [405, 414, 554, 486], "type": "image", "image_path": "f251113e59e96e759dd4a39a15db2785020092217050a8e1c76ea6f7de666e0c.jpg"}]}], "index": 19, "type": "image_body"}, {"bbox": [401, 494, 557, 523], "lines": [{"bbox": [401, 494, 557, 523], "spans": [{"bbox": [401, 494, 557, 523], "type": "text", "content": "湖南省委常委、常务副省长 张迎春湖南考试院巡检指挥中心"}]}], "index": 20, "type": "image_caption"}], "index": 19}, {"type": "image", "bbox": [581, 414, 736, 486], "blocks": [{"bbox": [581, 414, 736, 486], "lines": [{"bbox": [581, 414, 736, 486], "spans": [{"bbox": [581, 414, 736, 486], "type": "image", "image_path": "5a36b9793f380c580ab0790f549ee83c1fee039dc18fb98a07fa7ff02adc9ea8.jpg"}]}], "index": 21, "type": "image_body"}, {"bbox": [607, 494, 705, 523], "lines": [{"bbox": [607, 494, 705, 523], "spans": [{"bbox": [607, 494, 705, 523], "type": "text", "content": "广东省委书记 黄坤明华师大附中考点"}]}], "index": 22, "type": "image_caption"}], "index": 21}, {"type": "image", "bbox": [758, 414, 911, 486], "blocks": [{"bbox": [758, 414, 911, 486], "lines": [{"bbox": [758, 414, 911, 486], "spans": [{"bbox": [758, 414, 911, 486], "type": "image", "image_path": "caa767dca9347ba7ac2cb717f8f8f4bcbba918f31e0cb1bf09e7ca72c3c83261.jpg"}]}], "index": 23, "type": "image_body"}, {"bbox": [770, 494, 904, 523], "lines": [{"bbox": [770, 494, 904, 523], "spans": [{"bbox": [770, 494, 904, 523], "type": "text", "content": "四川省委副书记、省长 黄强成都七中考点"}]}], "index": 24, "type": "image_caption"}], "index": 23}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 4}, {"para_blocks": [{"type": "image", "bbox": [271, 135, 682, 449], "blocks": [{"bbox": [271, 135, 682, 449], "lines": [{"bbox": [271, 135, 682, 449], "spans": [{"bbox": [271, 135, 682, 449], "type": "image", "image_path": "c647146830406edba199039e6fb5b21db732ef78023004cdd6d5fc8e74d452d5.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 5}, {"para_blocks": [{"bbox": [36, 25, 645, 57], "type": "title", "lines": [{"bbox": [36, 25, 645, 57], "spans": [{"bbox": [36, 25, 645, 57], "type": "text", "content": "业务流程 | 人工智能赋能考试巡查系统，打造新质生产力"}]}], "index": 0, "level": 1}, {"bbox": [21, 85, 938, 126], "type": "text", "lines": [{"bbox": [21, 85, 938, 126], "spans": [{"bbox": [21, 85, 938, 126], "type": "text", "content": "通过AI巡考系统，赋予现有监考系统AI分析能力，实时分析考试过程中各种违规行为并及时反馈至监考人员进行干预，改变传统的考后人工视频核查模式，变“事后追溯为现场阻断（取证）”，大幅度提高考试监督效率、及时性、公正性。"}]}], "index": 1}, {"type": "image", "bbox": [29, 145, 936, 495], "blocks": [{"bbox": [29, 145, 936, 495], "lines": [{"bbox": [29, 145, 936, 495], "spans": [{"bbox": [29, 145, 936, 495], "type": "image", "image_path": "b44986e5cc40741aa78c78498fca2328c7d80b440a15c3f36332fb9009909578.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 6}, {"para_blocks": [{"bbox": [38, 25, 596, 57], "type": "title", "lines": [{"bbox": [38, 25, 596, 57], "spans": [{"bbox": [38, 25, 596, 57], "type": "text", "content": "系统架构 | 围绕考场巡考核心应用搭建解决方案架构"}]}], "index": 0, "level": 1}, {"bbox": [36, 88, 928, 129], "type": "text", "lines": [{"bbox": [36, 88, 928, 129], "spans": [{"bbox": [36, 88, 928, 129], "type": "text", "content": "基于移动云算网底座资源，联合成研/生态伙伴省级AI巡考平台，具备视频接入、AI分析、告警干预等功能模块，同时可为省、市、区各级考试院提供实时巡考工作管理系统（通过省平台权限分配实现），目前已具备AI巡考整体解决方案。"}]}], "index": 1}, {"type": "image", "bbox": [5, 162, 938, 528], "blocks": [{"bbox": [5, 162, 938, 528], "lines": [{"bbox": [5, 162, 938, 528], "spans": [{"bbox": [5, 162, 938, 528], "type": "image", "image_path": "debb35edcf2ff42920f659f09f1323ad25ce01bc5c47475357054f0a53c9f3c4.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 7}, {"para_blocks": [{"bbox": [36, 25, 476, 57], "type": "title", "lines": [{"bbox": [36, 25, 476, 57], "spans": [{"bbox": [36, 25, 476, 57], "type": "text", "content": "整体拓扑 | 云上部署模式，按需灵活选择"}]}], "index": 0, "level": 1}, {"bbox": [31, 81, 936, 122], "type": "text", "lines": [{"bbox": [31, 81, 936, 122], "spans": [{"bbox": [31, 81, 936, 122], "type": "text", "content": "以云边端架构部署，以云化算力为基础，搭载算力中心、巡考平台系统。云化部署模式下，考点无需部署计算设备，通过直连摄像头实现视频的采集与实时推流，视频数据经专线传到云端进行AI处理。算力中心对视频数据进行视频抽帧、算法分析判定。"}]}], "index": 1}, {"type": "image", "bbox": [31, 133, 930, 514], "blocks": [{"bbox": [31, 133, 930, 514], "lines": [{"bbox": [31, 133, 930, 514], "spans": [{"bbox": [31, 133, 930, 514], "type": "image", "image_path": "c24234208135185cc1eec9034e7d1c1b87f92922486f1cedadc8f8cde09d8a80.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 8}, {"para_blocks": [{"bbox": [36, 25, 499, 56], "type": "title", "lines": [{"bbox": [36, 25, 499, 56], "spans": [{"bbox": [36, 25, 499, 56], "type": "text", "content": "视频能力 | 兼容市面主流摄像头品牌及型号"}]}], "index": 0, "level": 1}, {"bbox": [31, 74, 936, 114], "type": "text", "lines": [{"bbox": [31, 74, 936, 114], "spans": [{"bbox": [31, 74, 936, 114], "type": "text", "content": "AI巡考平台支持以rtsp方式对考点/保密室的视频流进行直接拉取，该方式兼容市面主流摄像头品牌及型号，不需对接考点/保密室侧原有巡查系统。拉流的网络方案，可根据视频流汇聚规模及本地实际情况，选取IDC机房汇聚转发或新增终端设备转发上云。"}]}], "index": 1}, {"bbox": [186, 139, 299, 162], "type": "title", "lines": [{"bbox": [186, 139, 299, 162], "spans": [{"bbox": [186, 139, 299, 162], "type": "text", "content": "终端转发上云"}]}], "index": 2, "level": 1}, {"type": "image", "bbox": [66, 207, 125, 296], "blocks": [{"bbox": [66, 207, 125, 296], "lines": [{"bbox": [66, 207, 125, 296], "spans": [{"bbox": [66, 207, 125, 296], "type": "image", "image_path": "7c3a1cb90775afb5e77187219ce769e62e68795adb3b8b67df3085f6d6c2030b.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [295, 179, 345, 196], "type": "title", "lines": [{"bbox": [295, 179, 345, 196], "spans": [{"bbox": [295, 179, 345, 196], "type": "text", "content": "CPE设备"}]}], "index": 4, "level": 1}, {"bbox": [162, 221, 446, 282], "type": "text", "lines": [{"bbox": [162, 221, 446, 282], "spans": [{"bbox": [162, 221, 446, 282], "type": "text", "content": "- 客户侧的交换机接入CPE设备及数据专线，实现地址规整。- 可支撑50-200左右的视频流转发；"}]}], "index": 5}, {"bbox": [299, 310, 341, 327], "type": "title", "lines": [{"bbox": [299, 310, 341, 327], "spans": [{"bbox": [299, 310, 341, 327], "type": "text", "content": "路由器"}]}], "index": 6, "level": 1}, {"type": "image", "bbox": [58, 331, 136, 414], "blocks": [{"bbox": [58, 331, 136, 414], "lines": [{"bbox": [58, 331, 136, 414], "spans": [{"bbox": [58, 331, 136, 414], "type": "image", "image_path": "92dfaf311df83f7711a57c5a211c199ce068831df8a70961789a771c563ee1db.jpg"}]}], "index": 7, "type": "image_body"}], "index": 7}, {"bbox": [162, 349, 429, 409], "type": "text", "lines": [{"bbox": [162, 349, 429, 409], "spans": [{"bbox": [162, 349, 429, 409], "type": "text", "content": "- 客户侧的交换机接入路由器设备及数据专线，实现地址规整。- 可支撑2-10路左右的视频流转发；"}]}], "index": 8}, {"type": "image", "bbox": [561, 139, 895, 442], "blocks": [{"bbox": [561, 139, 895, 442], "lines": [{"bbox": [561, 139, 895, 442], "spans": [{"bbox": [561, 139, 895, 442], "type": "image", "image_path": "95ed35c09d565d74414d5b7d19edbf54e6d208b20eb59a1ee8b058dbd465aea6.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [129, 455, 373, 474], "type": "text", "lines": [{"bbox": [129, 455, 373, 474], "spans": [{"bbox": [129, 455, 373, 474], "type": "text", "content": "适合单考点，灵活的小规模视频流上云"}]}], "index": 10}, {"bbox": [599, 455, 829, 474], "type": "title", "lines": [{"bbox": [599, 455, 829, 474], "spans": [{"bbox": [599, 455, 829, 474], "type": "text", "content": "适合设备可利旧的大规模视频流上云"}]}], "index": 11, "level": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 9}, {"para_blocks": [{"bbox": [38, 23, 477, 54], "type": "title", "lines": [{"bbox": [38, 23, 477, 54], "spans": [{"bbox": [38, 23, 477, 54], "type": "text", "content": "算法能力 | 贴合政策要求，构建算力服务"}]}], "index": 0, "level": 1}, {"bbox": [29, 81, 552, 132], "type": "text", "lines": [{"bbox": [29, 81, 552, 132], "spans": [{"bbox": [29, 81, 552, 132], "type": "text", "content": "保密室智能巡检和考场智能巡查符合教育部教学司函[2024]13号中要求的人工智能识别分析异常行为分类44种"}]}], "index": 1}, {"type": "table", "bbox": [31, 178, 297, 304], "blocks": [{"bbox": [31, 178, 297, 304], "lines": [{"bbox": [31, 178, 297, 304], "spans": [{"bbox": [31, 178, 297, 304], "type": "table", "html": "<table><tr><td>序号</td><td>类别</td><td>智能分析内容</td></tr><tr><td>1</td><td rowspan=\"4\">保密室</td><td>保密室试卷清点环节告警</td></tr><tr><td>2</td><td>人员异常告警</td></tr><tr><td>3</td><td>保密室人员使用违规通讯工具告警</td></tr><tr><td>4</td><td>保密室携带物品外出告警</td></tr></table>", "image_path": "52f51f0de7ea69dac96f13495604de6a58968cd1c86398345ec8a199d82a8621.jpg"}]}], "index": 2, "type": "table_body"}], "index": 2}, {"type": "table", "bbox": [31, 319, 297, 514], "blocks": [{"bbox": [31, 319, 297, 514], "lines": [{"bbox": [31, 319, 297, 514], "spans": [{"bbox": [31, 319, 297, 514], "type": "table", "html": "<table><tr><td>序号</td><td>类别</td><td>智能分析内容</td></tr><tr><td>1</td><td rowspan=\"8\">考生群体</td><td>考生长时间不离场</td></tr><tr><td>2</td><td>多人向同一方向偏头</td></tr><tr><td>3</td><td>考生群体举手</td></tr><tr><td>4</td><td>考生群体起立</td></tr><tr><td>5</td><td>考生群体交头接耳</td></tr><tr><td>6</td><td>考生群体停止作答</td></tr><tr><td>7</td><td>考生群体考后继续作答</td></tr><tr><td>8</td><td>考生人数统计(缺考率分析)</td></tr></table>", "image_path": "03b12ab3e1387fc7a29499beb185ac205d749430ba2e3d2fae6a397f4d2eae80.jpg"}]}], "index": 3, "type": "table_body"}], "index": 3}, {"type": "table", "bbox": [324, 187, 552, 490], "blocks": [{"bbox": [324, 187, 552, 490], "lines": [{"bbox": [324, 187, 552, 490], "spans": [{"bbox": [324, 187, 552, 490], "type": "table", "html": "<table><tr><td>序号</td><td>类别</td><td>智能分析内容</td></tr><tr><td rowspan=\"12\">考生</td><td colspan=\"2\">提前作答</td></tr><tr><td colspan=\"2\">左右偏头</td></tr><tr><td colspan=\"2\">向后偏头</td></tr><tr><td colspan=\"2\">考生站立</td></tr><tr><td colspan=\"2\">传递可疑物品</td></tr><tr><td colspan=\"2\">捡可疑物品</td></tr><tr><td colspan=\"2\">携带可疑物品</td></tr><tr><td colspan=\"2\">手放桌下并埋头</td></tr><tr><td colspan=\"2\">考生中途出入考场</td></tr><tr><td colspan=\"2\">考生举手</td></tr><tr><td colspan=\"2\">销毁试卷、答案</td></tr><tr><td colspan=\"2\">考后继续作答</td></tr></table>", "image_path": "d08e7ecf962c216105e07691091eedf4133325eaf72dabacfd643d98cd11f451.jpg"}]}], "index": 4, "type": "table_body"}], "index": 4}, {"type": "table", "bbox": [583, 68, 926, 517], "blocks": [{"bbox": [583, 68, 926, 517], "lines": [{"bbox": [583, 68, 926, 517], "spans": [{"bbox": [583, 68, 926, 517], "type": "table", "html": "<table><tr><td>序号</td><td>类别</td><td>智能分析内容</td></tr><tr><td>1</td><td colspan=\"2\">监考员未按时到达考场</td></tr><tr><td>2</td><td colspan=\"2\">监考员未核对钟表时间</td></tr><tr><td>3</td><td colspan=\"2\">监考员未展示试卷袋/答题监考卡袋</td></tr><tr><td>4</td><td colspan=\"2\">监考员提前发卷</td></tr><tr><td>5</td><td colspan=\"2\">监考员未按时发放答题卡/试卷</td></tr><tr><td>6</td><td colspan=\"2\">监考员发卷不规范</td></tr><tr><td>7</td><td colspan=\"2\">监考员在考生位置就坐</td></tr><tr><td>8</td><td colspan=\"2\">监考员少于两人</td></tr><tr><td>9</td><td colspan=\"2\">监考员手持可疑物品</td></tr><tr><td>10</td><td colspan=\"2\">监考员注意力不集中</td></tr><tr><td>11</td><td colspan=\"2\">监考员未规范就坐</td></tr><tr><td>12</td><td colspan=\"2\">监考员长时间停留在考生旁边</td></tr><tr><td>13</td><td colspan=\"2\">监考员在固定区域时间过长</td></tr><tr><td>14</td><td colspan=\"2\">两名监考员在固定区域时间过长</td></tr><tr><td>15</td><td colspan=\"2\">监考员聊天</td></tr><tr><td>16</td><td colspan=\"2\">监考员抽烟</td></tr><tr><td>17</td><td colspan=\"2\">讲台桌面不整洁</td></tr><tr><td>18</td><td colspan=\"2\">监考员提前收卷</td></tr><tr><td>19</td><td colspan=\"2\">监考员延迟收卷</td></tr><tr><td>20</td><td colspan=\"2\">监考员未在讲合监督收卷</td></tr></table>", "image_path": "c3a76d118c349351d5dd44f85430bac7ce348eff67a29c2b02faa4ead4951838.jpg"}]}], "index": 5, "type": "table_body"}], "index": 5}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 10}, {"para_blocks": [{"bbox": [36, 25, 306, 56], "type": "title", "lines": [{"bbox": [36, 25, 306, 56], "spans": [{"bbox": [36, 25, 306, 56], "type": "text", "content": "底层能力 | 考点上云能力"}]}], "index": 0, "level": 1}, {"bbox": [23, 84, 933, 124], "type": "text", "lines": [{"bbox": [23, 84, 933, 124], "spans": [{"bbox": [23, 84, 933, 124], "type": "text", "content": "中国移动云能力中心已全面实现云网融合、云随网动，可支持全国各地考点快速实现就近、安全、高速入云，针对考试场景的特殊性，可灵活部署云主机、对象存储等短期使用的系统资源，可保障短时间内大量业务并发处理，事后资源快速释放，高效提供服务的同时又能确保服务的经济性。"}]}], "index": 1}, {"type": "image", "bbox": [16, 187, 443, 506], "blocks": [{"bbox": [16, 187, 443, 506], "lines": [{"bbox": [16, 187, 443, 506], "spans": [{"bbox": [16, 187, 443, 506], "type": "image", "image_path": "f3e63ee7f121a3ae4e1f34e27aff47efe47eacf6b30761e5cdcff058524c1cf9.jpg"}]}], "index": 2, "type": "image_body"}, {"bbox": [153, 164, 307, 181], "lines": [{"bbox": [153, 164, 307, 181], "spans": [{"bbox": [153, 164, 307, 181], "type": "text", "content": "招考院业务平台上云方案"}]}], "index": 3, "type": "image_caption"}], "index": 2}, {"type": "image", "bbox": [480, 162, 935, 511], "blocks": [{"bbox": [480, 162, 935, 511], "lines": [{"bbox": [480, 162, 935, 511], "spans": [{"bbox": [480, 162, 935, 511], "type": "image", "image_path": "7617f2e67fa5461ad2ea552d8643cc2f731a264a91a88b55839a5371d6e9f9ea.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 11}, {"para_blocks": [{"bbox": [29, 25, 347, 55], "type": "title", "lines": [{"bbox": [29, 25, 347, 55], "spans": [{"bbox": [29, 25, 347, 55], "type": "text", "content": "底层能力 | 云上安全防护能力"}]}], "index": 0, "level": 1}, {"bbox": [29, 74, 756, 127], "type": "text", "lines": [{"bbox": [29, 74, 756, 127], "spans": [{"bbox": [29, 74, 756, 127], "type": "text", "content": "移动云具备“可管、可控、可信”的高安全能力框架，为客户提供“跨地域、高可用、高可靠”的云计算产品及服务。移动公有云平台通过等保四级测评，为用户提供可信的平台安全保障以及可持续的云服务能力。通过混合云安全产品满足教育云专线构建的混合云场景下的安全防护需求。"}]}], "index": 1}, {"type": "image", "bbox": [29, 184, 503, 520], "blocks": [{"bbox": [29, 184, 503, 520], "lines": [{"bbox": [29, 184, 503, 520], "spans": [{"bbox": [29, 184, 503, 520], "type": "image", "image_path": "51120df566408a15e402589587e03f2150f1548b591ac7002edd752dda898520.jpg"}]}], "index": 2, "type": "image_body"}, {"bbox": [84, 161, 186, 177], "lines": [{"bbox": [84, 161, 186, 177], "spans": [{"bbox": [84, 161, 186, 177], "type": "text", "content": "监考视频专线入云"}]}], "index": 3, "type": "image_caption"}], "index": 2}, {"bbox": [539, 176, 613, 198], "type": "title", "lines": [{"bbox": [539, 176, 613, 198], "spans": [{"bbox": [539, 176, 613, 198], "type": "text", "content": "专线接入"}]}], "index": 4, "level": 1}, {"bbox": [537, 207, 874, 243], "type": "text", "lines": [{"bbox": [537, 207, 874, 243], "spans": [{"bbox": [537, 207, 874, 243], "type": "inline_equation", "content": "①"}, {"bbox": [537, 207, 874, 243], "type": "text", "content": " 考试视频云专线访问，不对外暴露公网IP地址；"}, {"bbox": [537, 207, 874, 243], "type": "inline_equation", "content": "②"}, {"bbox": [537, 207, 874, 243], "type": "text", "content": " VPC产品的网络ACL，仅限白名单，确保安全入云"}]}], "index": 5}, {"bbox": [539, 307, 614, 329], "type": "title", "lines": [{"bbox": [539, 307, 614, 329], "spans": [{"bbox": [539, 307, 614, 329], "type": "text", "content": "云内安全"}]}], "index": 6, "level": 1}, {"bbox": [537, 341, 923, 428], "type": "text", "lines": [{"bbox": [537, 341, 923, 428], "spans": [{"bbox": [537, 341, 923, 428], "type": "inline_equation", "content": "①"}, {"bbox": [537, 341, 923, 428], "type": "text", "content": " 其他租户的攻击：VPC逻辑隔离，资源地址为overlay层私网IP地址，网络层杜绝其他租户的攻击"}, {"bbox": [537, 341, 923, 428], "type": "inline_equation", "content": "②"}, {"bbox": [537, 341, 923, 428], "type": "text", "content": " 客户侧访问攻击：如校园监控视频带入相关病毒和攻击，通过部署云下一代防火墙、web全栈防护等产品，杜绝高危流量访问"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 12}, {"para_blocks": [{"bbox": [29, 25, 347, 56], "type": "title", "lines": [{"bbox": [29, 25, 347, 56], "spans": [{"bbox": [29, 25, 347, 56], "type": "text", "content": "底层能力 | 云下安全传输能力"}]}], "index": 0, "level": 1}, {"bbox": [29, 93, 777, 113], "type": "text", "lines": [{"bbox": [29, 93, 777, 113], "spans": [{"bbox": [29, 93, 777, 113], "type": "text", "content": "除摄像头本身自带的数据加密能力外，提供云专网和高强度IPsec加密两种方案确保视频数据入云时端到端全程安全可控。"}]}], "index": 1}, {"type": "image", "bbox": [26, 139, 936, 535], "blocks": [{"bbox": [26, 139, 936, 535], "lines": [{"bbox": [26, 139, 936, 535], "spans": [{"bbox": [26, 139, 936, 535], "type": "image", "image_path": "8504bd9bccfb44dfb1e47a731161422d0887af65832cfefd6ea024cfaffaa61f.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 13}, {"para_blocks": [{"bbox": [33, 24, 683, 56], "type": "title", "lines": [{"bbox": [33, 24, 683, 56], "spans": [{"bbox": [33, 24, 683, 56], "type": "text", "content": "服务能力 | 算法&算力&网络三位一体，打造一站式服务能力"}]}], "index": 0, "level": 1}, {"bbox": [241, 98, 369, 117], "type": "title", "lines": [{"bbox": [241, 98, 369, 117], "spans": [{"bbox": [241, 98, 369, 117], "type": "text", "content": "千兆考试专网全覆盖"}]}], "index": 1, "level": 1}, {"bbox": [40, 158, 420, 176], "type": "text", "lines": [{"bbox": [40, 158, 420, 176], "spans": [{"bbox": [40, 158, 420, 176], "type": "text", "content": "专网专用，考试业务不受影响，且网络传输物理隔离，安全得到保障"}]}], "index": 2}, {"bbox": [40, 187, 420, 205], "type": "text", "lines": [{"bbox": [40, 187, 420, 205], "spans": [{"bbox": [40, 187, 420, 205], "type": "text", "content": "千兆链接考点和核心机房，通过省干线SPN达到云端，架构稳定通畅"}]}], "index": 3}, {"type": "image", "bbox": [612, 84, 933, 230], "blocks": [{"bbox": [612, 84, 933, 230], "lines": [{"bbox": [612, 84, 933, 230], "spans": [{"bbox": [612, 84, 933, 230], "type": "image", "image_path": "eb33fa6b005e36eaa28dde7d865115f2c9ab77c3a6fc0a397b8dec0e6d8a9f49.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"bbox": [352, 245, 616, 264], "type": "title", "lines": [{"bbox": [352, 245, 616, 264], "spans": [{"bbox": [352, 245, 616, 264], "type": "text", "content": "大小模型协同训练，实现算力利用率最大化"}]}], "index": 5, "level": 1}, {"bbox": [48, 285, 528, 316], "type": "text", "lines": [{"bbox": [48, 285, 528, 316], "spans": [{"bbox": [48, 285, 528, 316], "type": "text", "content": "采用大模型，学习、分析人类行为，识别异常行为。利用大小模型蒸馏技术，迁移大模型知识至小模型，提升小模型准确度和模型推理效率。"}]}], "index": 6}, {"type": "image", "bbox": [42, 327, 930, 520], "blocks": [{"bbox": [42, 327, 930, 520], "lines": [{"bbox": [42, 327, 930, 520], "spans": [{"bbox": [42, 327, 930, 520], "type": "image", "image_path": "725aa3a5a769aa4d162b41112015c5ddd925f34e85765ac92ee7b862c2997389.jpg"}]}], "index": 7, "type": "image_body"}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 14}, {"para_blocks": [{"type": "image", "bbox": [271, 135, 681, 449], "blocks": [{"bbox": [271, 135, 681, 449], "lines": [{"bbox": [271, 135, 681, 449], "spans": [{"bbox": [271, 135, 681, 449], "type": "image", "image_path": "b146f37158107e69b78e7311ce7e72be5a45c98617a0cd76a42f1f8f8dfd8c1e.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 15}, {"para_blocks": [{"bbox": [29, 25, 300, 56], "type": "title", "lines": [{"bbox": [29, 25, 300, 56], "spans": [{"bbox": [29, 25, 300, 56], "type": "text", "content": "交付内容 | 云化部署模式"}]}], "index": 0, "level": 1}, {"bbox": [29, 88, 777, 108], "type": "text", "lines": [{"bbox": [29, 88, 777, 108], "spans": [{"bbox": [29, 88, 777, 108], "type": "text", "content": "云化模式，我们向客户交付的主要为一套AI分析服务、一个巡考业务平台、4级巡考管理系统（通过平台权限分配实现）。"}]}], "index": 1}, {"type": "image", "bbox": [24, 124, 930, 529], "blocks": [{"bbox": [24, 124, 930, 529], "lines": [{"bbox": [24, 124, 930, 529], "spans": [{"bbox": [24, 124, 930, 529], "type": "image", "image_path": "67c9e7d05d4d802190cdda38c5c0600009eabf26533b4ed266365914e05dff6a.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 16}, {"para_blocks": [{"bbox": [29, 24, 565, 56], "type": "title", "lines": [{"bbox": [29, 24, 565, 56], "spans": [{"bbox": [29, 24, 565, 56], "type": "text", "content": "云化部署 | 多场景全覆盖，云化部署成本优势明显"}]}], "index": 0, "level": 1}, {"bbox": [26, 81, 930, 121], "type": "text", "lines": [{"bbox": [26, 81, 930, 121], "spans": [{"bbox": [26, 81, 930, 121], "type": "text", "content": "基于移动云/政务云底座，打造省（市/县）- 考点多级部署架构，形成AI巡考云方案，实现考试巡考数据融合、融通、安全，资源集约化管理，即开即用，解决各考点分散建设成本高，无法统一管控等问题。"}]}], "index": 1}, {"type": "image", "bbox": [24, 139, 909, 489], "blocks": [{"bbox": [24, 139, 909, 489], "lines": [{"bbox": [24, 139, 909, 489], "spans": [{"bbox": [24, 139, 909, 489], "type": "image", "image_path": "7c7f3b252a603dd3be2b08a12302000f81da912a14837b85edda5ab23de76738.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 17}, {"para_blocks": [{"bbox": [36, 25, 597, 57], "type": "title", "lines": [{"bbox": [36, 25, 597, 57], "spans": [{"bbox": [36, 25, 597, 57], "type": "text", "content": "实施流程 | 标准化项目实施流程，快速响应客户需要"}]}], "index": 0, "level": 1}, {"bbox": [36, 91, 762, 112], "type": "text", "lines": [{"bbox": [36, 91, 762, 112], "spans": [{"bbox": [36, 91, 762, 112], "type": "text", "content": "AI巡考项目整个实施过程分为五大板块及30个关键步骤，为销售侧提供详实的操作手册，指导一线快速响应客户需求。"}]}], "index": 1}, {"bbox": [48, 131, 926, 177], "type": "text", "lines": [{"bbox": [48, 131, 926, 177], "spans": [{"bbox": [48, 131, 926, 177], "type": "text", "content": "项目责任人：省公司（负责专线业务交付）、成研院（成研牵头AI巡考平台、AI分析服务等产品供给）、云能力中心（负责算网方案匹配）、启明星辰（负责信息安全管理）"}]}], "index": 2}, {"bbox": [71, 217, 129, 235], "type": "title", "lines": [{"bbox": [71, 217, 129, 235], "spans": [{"bbox": [71, 217, 129, 235], "type": "text", "content": "需求对接"}]}], "index": 3, "level": 1}, {"bbox": [256, 217, 314, 235], "type": "title", "lines": [{"bbox": [256, 217, 314, 235], "spans": [{"bbox": [256, 217, 314, 235], "type": "text", "content": "方案设计"}]}], "index": 4, "level": 1}, {"bbox": [443, 217, 494, 235], "type": "title", "lines": [{"bbox": [443, 217, 494, 235], "spans": [{"bbox": [443, 217, 494, 235], "type": "text", "content": "商务沟通"}]}], "index": 5, "level": 1}, {"bbox": [594, 217, 720, 252], "type": "title", "lines": [{"bbox": [594, 217, 720, 252], "spans": [{"bbox": [594, 217, 720, 252], "type": "text", "content": "测试验证及网络准备"}]}], "index": 6, "level": 1}, {"bbox": [801, 215, 860, 233], "type": "title", "lines": [{"bbox": [801, 215, 860, 233], "spans": [{"bbox": [801, 215, 860, 233], "type": "text", "content": "交付实施"}]}], "index": 7, "level": 1}, {"bbox": [47, 276, 170, 307], "type": "text", "lines": [{"bbox": [47, 276, 170, 307], "spans": [{"bbox": [47, 276, 170, 307], "type": "text", "content": "对接客户，明确客户需求，确认项目可行性"}]}], "index": 8}, {"bbox": [45, 332, 168, 409], "type": "text", "lines": [{"bbox": [45, 332, 168, 409], "spans": [{"bbox": [45, 332, 168, 409], "type": "text", "content": "1. 客户需求调研2. 考点、考场清单3. 考场硬件部署情况4. 带宽、网络环境评估结果"}]}], "index": 9}, {"bbox": [223, 276, 350, 320], "type": "text", "lines": [{"bbox": [223, 276, 350, 320], "spans": [{"bbox": [223, 276, 350, 320], "type": "text", "content": "输出解决方案，处理客户服务模式及网络带宽的问题"}]}], "index": 10}, {"bbox": [221, 324, 345, 415], "type": "text", "lines": [{"bbox": [221, 324, 345, 415], "spans": [{"bbox": [221, 324, 345, 415], "type": "text", "content": "5. 专线短租解决方案6. 专线补点解决方案7. 云网算力测算表8. 算力调度解决方案9. 上云解决方案评估10. 平台部署解决方案"}]}], "index": 11}, {"bbox": [408, 273, 533, 304], "type": "text", "lines": [{"bbox": [408, 273, 533, 304], "spans": [{"bbox": [408, 273, 533, 304], "type": "text", "content": "基于客户需求和试点验证结果，推进商务流程"}]}], "index": 12}, {"bbox": [408, 321, 508, 412], "type": "text", "lines": [{"bbox": [408, 321, 508, 412], "spans": [{"bbox": [408, 321, 508, 412], "type": "text", "content": "11. 确认服务内容12. 确认实施周期13. 沟通商务价格14. 撰写招标文件15. 参与投标16. 签订合作合同"}]}], "index": 13}, {"bbox": [587, 267, 729, 300], "type": "text", "lines": [{"bbox": [587, 267, 729, 300], "spans": [{"bbox": [587, 267, 729, 300], "type": "text", "content": "针对高利害考试组织考试前试点，确认方案可行性"}]}], "index": 14}, {"bbox": [583, 307, 741, 336], "type": "text", "lines": [{"bbox": [583, 307, 741, 336], "spans": [{"bbox": [583, 307, 741, 336], "type": "text", "content": "17. 专线申请、带宽升级18. 跨省云专线评估"}]}], "index": 15}, {"bbox": [583, 338, 741, 366], "type": "text", "lines": [{"bbox": [583, 338, 741, 366], "spans": [{"bbox": [583, 338, 741, 366], "type": "text", "content": "19. 云-网-应用-安全整体测试20. 组织试点实施，测试硬件"}]}], "index": 16}, {"bbox": [583, 367, 741, 381], "type": "text", "lines": [{"bbox": [583, 367, 741, 381], "spans": [{"bbox": [583, 367, 741, 381], "type": "text", "content": "及网络环境"}]}], "index": 17}, {"bbox": [583, 382, 741, 411], "type": "text", "lines": [{"bbox": [583, 382, 741, 411], "spans": [{"bbox": [583, 382, 741, 411], "type": "text", "content": "21. 测试结果验证，明确分工界面，形成实施方案"}]}], "index": 18}, {"bbox": [583, 412, 741, 441], "type": "text", "lines": [{"bbox": [583, 412, 741, 441], "spans": [{"bbox": [583, 412, 741, 441], "type": "text", "content": "22. 形成实施指南，完善组织机制"}]}], "index": 19}, {"bbox": [768, 269, 895, 315], "type": "text", "lines": [{"bbox": [768, 269, 895, 315], "spans": [{"bbox": [768, 269, 895, 315], "type": "text", "content": "基于客户合同需求进行交付实施，完成项目验收"}]}], "index": 20}, {"bbox": [768, 323, 871, 366], "type": "text", "lines": [{"bbox": [768, 323, 871, 366], "spans": [{"bbox": [768, 323, 871, 366], "type": "text", "content": "23. 专线补点实施24. 专线扩容实施25. 专线带宽检测"}]}], "index": 21}, {"bbox": [768, 367, 904, 431], "type": "text", "lines": [{"bbox": [768, 367, 904, 431], "spans": [{"bbox": [768, 367, 904, 431], "type": "text", "content": "26. 云资源交付部署27. 考场硬件摄像头检测28. 考试期间重点维保29. 提供AI异常识别报告30. 考后资源回收"}]}], "index": 22}, {"type": "image", "bbox": [768, 460, 885, 499], "blocks": [{"bbox": [768, 460, 885, 499], "lines": [{"bbox": [768, 460, 885, 499], "spans": [{"bbox": [768, 460, 885, 499], "type": "image", "image_path": "a9de81f3447ab0a929a2ccf00b1c55cad806772b7aa548152f7d807c800f09e1.jpg"}]}], "index": 23, "type": "image_body"}], "index": 23}, {"bbox": [71, 462, 117, 480], "type": "title", "lines": [{"bbox": [71, 462, 117, 480], "spans": [{"bbox": [71, 462, 117, 480], "type": "text", "content": "首公司"}]}], "index": 24, "level": 1}, {"bbox": [256, 462, 347, 496], "type": "text", "lines": [{"bbox": [256, 462, 347, 496], "spans": [{"bbox": [256, 462, 347, 496], "type": "text", "content": "成研院、云能、省公司"}]}], "index": 25}, {"bbox": [443, 462, 528, 496], "type": "text", "lines": [{"bbox": [443, 462, 528, 496], "spans": [{"bbox": [443, 462, 528, 496], "type": "text", "content": "省公司、云能、成研院"}]}], "index": 26}, {"bbox": [612, 462, 707, 496], "type": "text", "lines": [{"bbox": [612, 462, 707, 496], "spans": [{"bbox": [612, 462, 707, 496], "type": "text", "content": "省公司、云能、成研院"}]}], "index": 27}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 18}, {"para_blocks": [{"bbox": [47, 25, 144, 54], "type": "title", "lines": [{"bbox": [47, 25, 144, 54], "spans": [{"bbox": [47, 25, 144, 54], "type": "text", "content": "支撑团队"}]}], "index": 0, "level": 1}, {"bbox": [24, 76, 904, 116], "type": "text", "lines": [{"bbox": [24, 76, 904, 116], "spans": [{"bbox": [24, 76, 904, 116], "type": "text", "content": "集团教育BU全国指导调度，联合成研、云能力中心对省接口，联合产品方案专家支撑团队组成的AI巡考业务攻坚团队，各司其职，全方位支撑各省AI巡考业务拓展。"}]}], "index": 1}, {"type": "table", "bbox": [50, 127, 890, 457], "blocks": [{"bbox": [50, 127, 890, 457], "lines": [{"bbox": [50, 127, 890, 457], "spans": [{"bbox": [50, 127, 890, 457], "type": "table", "html": "<table><tr><td colspan=\"8\">接口人</td></tr><tr><td>区域</td><td>省</td><td>成研院接口人</td><td>云能力接口人</td><td>省</td><td>成研院接口人</td><td>云能力接口人</td><td>成研产品专家</td></tr><tr><td rowspan=\"4\">北方</td><td>北京</td><td>秦瑞13910077231</td><td>杨剑浩13810823682</td><td>内蒙古</td><td>李明星15210087500</td><td>李杨18896726673</td><td></td></tr><tr><td>天津</td><td>李红伟13651265155</td><td>曹靖林18301666067</td><td>辽宁</td><td>郭玉鹏13811325515</td><td>张骥初15001155605</td><td>张峻宁</td></tr><tr><td>河北</td><td>孙璋13811590180</td><td>王若生15011310273</td><td>吉林</td><td>周一鸣13520109656</td><td>于越13810609982</td><td>18802881120</td></tr><tr><td>山西</td><td>王志军13552395267</td><td>曹静18896726125</td><td>黑龙江</td><td>刘斌13810723875</td><td>刘广超13810621712</td><td></td></tr><tr><td rowspan=\"3\">华东</td><td>上海</td><td>周伟13808012684</td><td>李航18516735116</td><td>安徽</td><td>陈冲15882196958</td><td>王丁17821759008</td><td></td></tr><tr><td>江苏</td><td>许喆18802880169</td><td>骆波13482244481</td><td>山东</td><td>李虎18802880173</td><td>王志斌15102133854</td><td>李思睿</td></tr><tr><td>浙江</td><td>周伟13808012684</td><td>许茜18896724675</td><td>/</td><td>/</td><td>/</td><td></td></tr><tr><td rowspan=\"2\">华南</td><td>福建</td><td>谢池泉15002885005</td><td>詹锦涛18305929559</td><td>广西</td><td>刘振华18380462316</td><td>梁玉文13481395933</td><td>陈伟</td></tr><tr><td>广东</td><td>梁达成18802880756</td><td>吴毓华19866066372</td><td>海南</td><td>牛昊15882488684</td><td>冯推发13627596027</td><td>18802880476</td></tr><tr><td rowspan=\"2\">华中</td><td>江西</td><td>王彦直13551009906</td><td>黎昱杰13822280741</td><td>湖北</td><td>王炜13802881906</td><td>张欣18819391686</td><td>张洪健</td></tr><tr><td>河南</td><td>韩超朴18802880810</td><td>杨浩鹏18320775644</td><td>湖南</td><td>程达18802881102</td><td>吴昊18898887322</td><td>18382018142</td></tr><tr><td rowspan=\"3\">西北</td><td>陕西</td><td>颜微子15882230465</td><td>李曦13909186251</td><td>宁夏</td><td>韩司宇18802880120</td><td>何龙13709042001</td><td></td></tr><tr><td>甘肃</td><td>曾杰18802880820</td><td>李浩18224083849</td><td>新疆</td><td>杨杰木冈13880077101</td><td>李阳15261810028</td><td>廖薪棋</td></tr><tr><td>青海</td><td>颜微子15882230465</td><td>张潇瀚18848330991</td><td>/</td><td>/</td><td>/</td><td></td></tr><tr><td rowspan=\"3\">西南</td><td>四川</td><td>邓子煜18802880107</td><td>王斯蕙13678089469</td><td>重庆</td><td>陈默18802880608</td><td>高萱15652935181</td><td></td></tr><tr><td>贵州</td><td>刘天歌18802881013</td><td>张正丹18780014145</td><td>西藏</td><td>邓益迁18200118628</td><td>何鑫18228013384</td><td>15196614076</td></tr><tr><td>云南</td><td>甘露1882087177</td><td>王天庆13678706023</td><td>/</td><td>/</td><td>/</td><td></td></tr></table>", "image_path": "046e9340ffc03d6351154f390b8d8d22b2041aed315beb69e7c3b7fe24c46559.jpg"}]}], "index": 2, "type": "table_body"}], "index": 2}, {"type": "table", "bbox": [50, 463, 890, 528], "blocks": [{"bbox": [50, 463, 890, 528], "lines": [{"bbox": [50, 463, 890, 528], "spans": [{"bbox": [50, 463, 890, 528], "type": "table", "html": "<table><tr><td>成研院</td><td>云能力中心</td></tr><tr><td>曾达</td><td>赵栖平</td></tr><tr><td>教育产品/二中心产品负责人</td><td>行业拓展三部解决方案经理</td></tr><tr><td>18802880205</td><td>18262027631</td></tr></table>", "image_path": "3b68bc1917c6c425408bcd4f6b8b2902f3b0de0ba9d7be4d59fb842ed437f9a5.jpg"}]}], "index": 3, "type": "table_body"}], "index": 3}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 19}, {"para_blocks": [{"type": "image", "bbox": [271, 135, 681, 451], "blocks": [{"bbox": [271, 135, 681, 451], "lines": [{"bbox": [271, 135, 681, 451], "spans": [{"bbox": [271, 135, 681, 451], "type": "image", "image_path": "1994115b173ef225f19904740dd60e182eb83a5f1995203a1ccc990617082c50.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 20}, {"para_blocks": [{"bbox": [37, 23, 406, 54], "type": "title", "lines": [{"bbox": [37, 23, 406, 54], "spans": [{"bbox": [37, 23, 406, 54], "type": "text", "content": "案例介绍 | 四川省AI巡检服务项目"}]}], "index": 0, "level": 1}, {"bbox": [201, 92, 727, 115], "type": "title", "lines": [{"bbox": [201, 92, 727, 115], "spans": [{"bbox": [201, 92, 727, 115], "type": "text", "content": "覆盖四川全省21个市州，357个高考考点和196个保密室，2.2万个摄像头"}]}], "index": 1, "level": 1}, {"bbox": [240, 124, 690, 145], "type": "text", "lines": [{"bbox": [240, 124, 690, 145], "spans": [{"bbox": [240, 124, 690, 145], "type": "text", "content": "2024年6月3日至6月11日，平台共计产生AI分析预警22149条"}]}], "index": 2}, {"bbox": [74, 173, 144, 193], "type": "title", "lines": [{"bbox": [74, 173, 144, 193], "spans": [{"bbox": [74, 173, 144, 193], "type": "text", "content": "技术方案"}]}], "index": 3, "level": 1}, {"bbox": [38, 210, 367, 347], "type": "text", "lines": [{"bbox": [38, 210, 367, 347], "spans": [{"bbox": [38, 210, 367, 347], "type": "text", "content": "平台系统：启用“四川省高考考场实时智能巡查”和“四川省高考保密室实时智能巡检”平台，具备视频接入、AI分析、告警干预等功能模块，同时可为省、市、县、考点四级提供实时AI巡检管理"}]}], "index": 4}, {"bbox": [38, 355, 372, 463], "type": "text", "lines": [{"bbox": [38, 355, 372, 463], "spans": [{"bbox": [38, 355, 372, 463], "type": "text", "content": "组网方案：采用“分支接入+市州汇聚+灵活调度”，实现网络安全接入、客户侧0配置、随心拉流的敏捷、极简组网，支撑双云双AI大规模算力资源、跨地域云化算力调度。"}]}], "index": 5}, {"bbox": [479, 173, 547, 193], "type": "title", "lines": [{"bbox": [479, 173, 547, 193], "spans": [{"bbox": [479, 173, 547, 193], "type": "text", "content": "实施成效"}]}], "index": 6, "level": 1}, {"bbox": [441, 210, 671, 376], "type": "text", "lines": [{"bbox": [441, 210, 671, 376], "spans": [{"bbox": [441, 210, 671, 376], "type": "text", "content": "标杆效益：国家考试院全国试点中唯一实现省级全重点位覆盖的标杆，同时也是全国试点中唯一完全实现云化部署的落地案例客户好评：获得省领导、省教育厅、省考试院的一致认可。"}]}], "index": 7}, {"bbox": [441, 384, 672, 462], "type": "text", "lines": [{"bbox": [441, 384, 672, 462], "spans": [{"bbox": [441, 384, 672, 462], "type": "text", "content": "社会效益：39家媒体报道，其中包括人民日报、人民邮电报、新华网、今日头条"}]}], "index": 8}, {"type": "image", "bbox": [703, 162, 864, 288], "blocks": [{"bbox": [703, 162, 864, 288], "lines": [{"bbox": [703, 162, 864, 288], "spans": [{"bbox": [703, 162, 864, 288], "type": "image", "image_path": "f1888e7751e50fc5f0b1ef773b507b20211ba84862674eb4cb96719c7cd7fd74.jpg"}]}], "index": 9, "type": "image_body"}, {"bbox": [683, 292, 880, 307], "lines": [{"bbox": [683, 292, 880, 307], "spans": [{"bbox": [683, 292, 880, 307], "type": "text", "content": "四川省委副书记、省长黄强亲临考察"}]}], "index": 10, "type": "image_caption"}], "index": 9}, {"type": "image", "bbox": [703, 322, 873, 491], "blocks": [{"bbox": [703, 322, 873, 491], "lines": [{"bbox": [703, 322, 873, 491], "spans": [{"bbox": [703, 322, 873, 491], "type": "image", "image_path": "8d4a23b6f06934a82d37c60f1b84f4a8df04ea5731b89aba074529c65f39e57f.jpg"}]}], "index": 11, "type": "image_body"}], "index": 11}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 21}, {"para_blocks": [{"bbox": [37, 23, 406, 54], "type": "title", "lines": [{"bbox": [37, 23, 406, 54], "spans": [{"bbox": [37, 23, 406, 54], "type": "text", "content": "案例介绍 | 广东省AI巡检服务项目"}]}], "index": 0, "level": 1}, {"bbox": [125, 105, 789, 127], "type": "title", "lines": [{"bbox": [125, 105, 789, 127], "spans": [{"bbox": [125, 105, 789, 127], "type": "text", "content": "覆盖广东全省21个市州386个高考考点，1.8万考场、51万余名高考考生，占比超过全省 "}, {"bbox": [125, 105, 789, 127], "type": "inline_equation", "content": "67\\%"}]}], "index": 1, "level": 1}, {"bbox": [75, 157, 144, 177], "type": "title", "lines": [{"bbox": [75, 157, 144, 177], "spans": [{"bbox": [75, 157, 144, 177], "type": "text", "content": "技术方案"}]}], "index": 2, "level": 1}, {"bbox": [38, 188, 369, 440], "type": "text", "lines": [{"bbox": [38, 188, 369, 440], "spans": [{"bbox": [38, 188, 369, 440], "type": "text", "content": "平台系统：启用“广东省高考考场实时智能巡查”平台，并与原有巡查系统并存，保证了AI到校的最后一公里，同时可为省、市、县、考点四级实时AI巡检管理服务能力。组网方案：技术方案采用“移动边缘云AI视频分析服务+考点侧部署视频拉流服务”架构，同时提供移动大云备份，实现网络安全接入、组网拉流的敏捷畅通、高并发期间的负载均衡。"}]}], "index": 3}, {"bbox": [476, 157, 543, 177], "type": "title", "lines": [{"bbox": [476, 157, 543, 177], "spans": [{"bbox": [476, 157, 543, 177], "type": "text", "content": "实施成效"}]}], "index": 4, "level": 1}, {"bbox": [434, 190, 653, 355], "type": "text", "lines": [{"bbox": [434, 190, 653, 355], "spans": [{"bbox": [434, 190, 653, 355], "type": "text", "content": "标杆效益：广东首次进行大规模云化部署应用在高考，过去试点期间均采用本地部署客户好评：获得省考试院高度赞赏，指出“完成了不可能完成的任务”。"}]}], "index": 5}, {"bbox": [434, 363, 653, 472], "type": "text", "lines": [{"bbox": [434, 363, 653, 472], "spans": [{"bbox": [434, 363, 653, 472], "type": "text", "content": "边际效益：定义“AI巡检元年”的势头，后续共同探讨将AI巡检应用于自考、研究生考试、其他社会考试的实施方案"}]}], "index": 6}, {"type": "image", "bbox": [688, 155, 883, 287], "blocks": [{"bbox": [688, 155, 883, 287], "lines": [{"bbox": [688, 155, 883, 287], "spans": [{"bbox": [688, 155, 883, 287], "type": "image", "image_path": "9a1b012f4f535cc3a9543cd73f4b0993569d70fb29087ed2a115ba6bdafec267.jpg"}]}], "index": 7, "type": "image_body"}, {"bbox": [695, 291, 852, 307], "lines": [{"bbox": [695, 291, 852, 307], "spans": [{"bbox": [695, 291, 852, 307], "type": "text", "content": "广东省委书记黄坤明视察考点"}]}], "index": 8, "type": "image_caption"}], "index": 7}, {"type": "image", "bbox": [693, 310, 880, 516], "blocks": [{"bbox": [693, 310, 880, 516], "lines": [{"bbox": [693, 310, 880, 516], "spans": [{"bbox": [693, 310, 880, 516], "type": "image", "image_path": "bde27f6b411d216b56842b343db8d11ea908401911cf11e75e90234b1ecbdaf3.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 22}, {"para_blocks": [{"bbox": [37, 23, 406, 54], "type": "title", "lines": [{"bbox": [37, 23, 406, 54], "spans": [{"bbox": [37, 23, 406, 54], "type": "text", "content": "案例介绍 | 湖南省AI巡检服务项目"}]}], "index": 0, "level": 1}, {"bbox": [273, 89, 679, 143], "type": "text", "lines": [{"bbox": [273, 89, 679, 143], "spans": [{"bbox": [273, 89, 679, 143], "type": "text", "content": "覆盖湖南全省14个市州，121个保密室，242个摄像头  2024年6月3日至6月10日，共计产生AI分析预警5083条"}]}], "index": 1}, {"bbox": [75, 189, 144, 209], "type": "title", "lines": [{"bbox": [75, 189, 144, 209], "spans": [{"bbox": [75, 189, 144, 209], "type": "text", "content": "技术方案"}]}], "index": 2, "level": 1}, {"bbox": [38, 220, 374, 444], "type": "text", "lines": [{"bbox": [38, 220, 374, 444], "spans": [{"bbox": [38, 220, 374, 444], "type": "text", "content": "平台系统：启用“保密室实时智能巡检”平台，快速定位发现保密室试卷清点环节告警、人员异常告警、保密室人员使用违规通讯工具告警、保密室携带物品外出告警等多类疑似异常行为。组网方案：采用全云专网部署方案，区县直接云，并部署云防火墙、Web防护等安全产品。考点侧采用双网卡模式，以强化云化部署的安全性和保密性"}]}], "index": 3}, {"bbox": [463, 189, 534, 208], "type": "title", "lines": [{"bbox": [463, 189, 534, 208], "spans": [{"bbox": [463, 189, 534, 208], "type": "text", "content": "实施成效"}]}], "index": 4, "level": 1}, {"bbox": [429, 224, 616, 292], "type": "text", "lines": [{"bbox": [429, 224, 616, 292], "spans": [{"bbox": [429, 224, 616, 292], "type": "text", "content": "标杆效益：湖南首次实现高考考试保密室的全面实现云化部署的落地"}]}], "index": 5}, {"bbox": [429, 300, 615, 343], "type": "text", "lines": [{"bbox": [429, 300, 615, 343], "spans": [{"bbox": [429, 300, 615, 343], "type": "text", "content": "客户好评：获得省领导、省考试院的一致认可。"}]}], "index": 6}, {"bbox": [429, 349, 615, 444], "type": "text", "lines": [{"bbox": [429, 349, 615, 444], "spans": [{"bbox": [429, 349, 615, 444], "type": "text", "content": "边际效益：云+专线在考试业务上重要性得到认可，未来计划完成考务平台以及考务专线的建设"}]}], "index": 7}, {"type": "image", "bbox": [683, 177, 895, 287], "blocks": [{"bbox": [683, 177, 895, 287], "lines": [{"bbox": [683, 177, 895, 287], "spans": [{"bbox": [683, 177, 895, 287], "type": "image", "image_path": "06636739e90a4a3f359af26f28d6e301fc37a8aa24efad330a4e8b0bbcd65614.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}, {"bbox": [698, 290, 883, 320], "type": "text", "lines": [{"bbox": [698, 290, 883, 320], "spans": [{"bbox": [698, 290, 883, 320], "type": "text", "content": "湖南省委常委、常务副省长张迎春长沙市明德中学考点"}]}], "index": 9}, {"bbox": [695, 328, 844, 342], "type": "title", "lines": [{"bbox": [695, 328, 844, 342], "spans": [{"bbox": [695, 328, 844, 342], "type": "text", "content": "湖南移动AI智能巡查助力高考保障"}]}], "index": 10, "level": 1}, {"bbox": [695, 348, 758, 355], "type": "text", "lines": [{"bbox": [695, 348, 758, 355], "spans": [{"bbox": [695, 348, 758, 355], "type": "text", "content": "虹联 2024- 06- 12 13:38"}]}], "index": 11}, {"bbox": [695, 366, 887, 382], "type": "text", "lines": [{"bbox": [695, 366, 887, 382], "spans": [{"bbox": [695, 366, 887, 382], "type": "text", "content": "保障高考，也有AI加持了。在2024年高考中，湖南移动高考AI智能巡查项目实现了全省122个区县全覆盖，发挥了大作用。"}]}], "index": 12}, {"bbox": [695, 388, 887, 429], "type": "text", "lines": [{"bbox": [695, 388, 887, 429], "spans": [{"bbox": [695, 388, 887, 429], "type": "text", "content": "施行后，省级保密室安防主要采取以“人防”为主的模式，在保密室部署着安力量进行安全保卫，清理人力大，监控盲区多，工作效率低。根据有关要求，湖南移动要配置严格情报系统中入侵情报中心、有线中心、成都研究院、云能中心组建项目专项团队，调度有线、快速查询200人交付人员，按时完成交付任务，并派遣保障小组在湖南省考试局驻点保障，72小时对报情保障项目所涉平台、网络的正常稳定运行。"}]}], "index": 13}, {"bbox": [695, 436, 887, 474], "type": "text", "lines": [{"bbox": [695, 436, 887, 474], "spans": [{"bbox": [695, 436, 887, 474], "type": "text", "content": "高考AI智能巡查项目通过人工智能异常分析能力，进行全流程信息化管理，异常情况及时通知管理人员。同时，湖南移动建设了全新的高考保密室云专网，通过安全云专线方式，仅具备保密室业务直接接入移动云。实现多个省区县视频存储上云，大规模使用GPU智智报情开展AI巡查，并且为下一步接入全省300+考点、10000+考室的二期项目奠定基础。"}]}], "index": 14}, {"bbox": [695, 484, 885, 501], "type": "text", "lines": [{"bbox": [695, 484, 885, 501], "spans": [{"bbox": [695, 484, 885, 501], "type": "text", "content": "保障期间，有关路段视频录像视频能进考中心，随机抽查全省高考工作现场情况和数据分析，并对实时目的和施工工期、云计算等信息技术赋能提高考场予高度肯定。"}]}], "index": 15}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 23}, {"para_blocks": [{"bbox": [428, 259, 504, 298], "type": "title", "lines": [{"bbox": [428, 259, 504, 298], "spans": [{"bbox": [428, 259, 504, 298], "type": "text", "content": "谢谢"}]}], "index": 0, "level": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 24}], "_backend": "vlm", "_version_name": "2.1.9"}