{"pdf_info": [{"para_blocks": [{"bbox": [237, 187, 734, 240], "type": "title", "lines": [{"bbox": [237, 187, 734, 240], "spans": [{"bbox": [237, 187, 734, 240], "type": "text", "content": "轻量化智慧酒店解决方案"}]}], "index": 0, "level": 1}, {"bbox": [343, 270, 616, 318], "type": "text", "lines": [{"bbox": [343, 270, 616, 318], "spans": [{"bbox": [343, 270, 616, 318], "type": "text", "content": "客户介绍材料"}]}], "index": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 0}, {"para_blocks": [{"type": "image", "bbox": [218, 184, 784, 343], "blocks": [{"bbox": [218, 184, 784, 343], "lines": [{"bbox": [218, 184, 784, 343], "spans": [{"bbox": [218, 184, 784, 343], "type": "image", "image_path": "9ece52ad86f33ecb793b9e68ab3f35c512b710f72381a2c862efcd58797f17a8.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 1}, {"para_blocks": [{"bbox": [64, 21, 527, 51], "type": "title", "lines": [{"bbox": [64, 21, 527, 51], "spans": [{"bbox": [64, 21, 527, 51], "type": "text", "content": "1、行业需求分析-酒店信息化服务需求迫切"}]}], "index": 0, "level": 1}, {"bbox": [24, 70, 931, 151], "type": "text", "lines": [{"bbox": [24, 70, 931, 151], "spans": [{"bbox": [24, 70, 931, 151], "type": "text", "content": "- 行业现状：酒店行业信息化技术发展应用处于发展期，酒店普遍缺乏统一数字化管理能力，多平台维护重，上手难；单个酒店要发展智能化，普遍需要对接至少6-7家供应商，运维复杂，管理混乱，操作平台多等问题，无一站式解决方案，尤其在酒店业务管理、智能设备管理、网络管理三个方面具备较大的数字化升级需求。"}]}], "index": 1}, {"bbox": [443, 159, 504, 179], "type": "title", "lines": [{"bbox": [443, 159, 504, 179], "spans": [{"bbox": [443, 159, 504, 179], "type": "text", "content": "需求分析"}]}], "index": 2, "level": 1}, {"bbox": [40, 193, 552, 212], "type": "text", "lines": [{"bbox": [40, 193, 552, 212], "spans": [{"bbox": [40, 193, 552, 212], "type": "text", "content": "后疫情时代，旅游业复苏，行业竞争激烈，酒店行业需要提供更优质的服务提高客单价及入住率。"}]}], "index": 3}, {"bbox": [42, 216, 864, 278], "type": "text", "lines": [{"bbox": [42, 216, 864, 278], "spans": [{"bbox": [42, 216, 864, 278], "type": "text", "content": "- 迫切希望提升客人客房智能化体验：除了设施完善、环境整洁、服务热情外，客人对酒店智能化、信息化水平的要求也越来越高。- 迫切希望提升酒店运营水平：IT系统的大规模应用将有效帮助酒店实现降本增效的目标，但行业缺乏专业IT人才，IT建设与运维缺乏长期大额的资金投入。- 迫切希望优化wifi网络体验：满足客人流畅的无缝上网体验、智能设备联网以及公安对安全审计要求。"}]}], "index": 4}, {"bbox": [47, 315, 330, 337], "type": "title", "lines": [{"bbox": [47, 315, 330, 337], "spans": [{"bbox": [47, 315, 330, 337], "type": "text", "content": "SaaS酒店系统是酒店数字化升级的主干"}]}], "index": 5, "level": 1}, {"type": "image", "bbox": [45, 342, 389, 480], "blocks": [{"bbox": [45, 342, 389, 480], "lines": [{"bbox": [45, 342, 389, 480], "spans": [{"bbox": [45, 342, 389, 480], "type": "image", "image_path": "f7d251daf49cf10e202d108df9ab400d3391dee5d7ac00257cfa49da8b62b5ec.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}, {"bbox": [40, 490, 390, 517], "type": "text", "lines": [{"bbox": [40, 490, 390, 517], "spans": [{"bbox": [40, 490, 390, 517], "type": "text", "content": "一个完整的平台将门店、业态、OTA、智能硬件、酒店各方人员、客人进行及时有效的连接和互动。"}]}], "index": 7}, {"bbox": [480, 315, 838, 336], "type": "title", "lines": [{"bbox": [480, 315, 838, 336], "spans": [{"bbox": [480, 315, 838, 336], "type": "text", "content": "智能化设备统一管控是酒店数字化升级的大势所趋"}]}], "index": 8, "level": 1}, {"bbox": [503, 353, 572, 370], "type": "title", "lines": [{"bbox": [503, 353, 572, 370], "spans": [{"bbox": [503, 353, 572, 370], "type": "text", "content": "发展痛点："}]}], "index": 9, "level": 1}, {"bbox": [506, 380, 638, 432], "type": "text", "lines": [{"bbox": [506, 380, 638, 432], "spans": [{"bbox": [506, 380, 638, 432], "type": "text", "content": "设备多难统一管理数据分散难价值挖掘"}]}], "index": 10}, {"bbox": [503, 440, 572, 457], "type": "title", "lines": [{"bbox": [503, 440, 572, 457], "spans": [{"bbox": [503, 440, 572, 457], "type": "text", "content": "产品需求："}]}], "index": 11, "level": 1}, {"bbox": [501, 477, 867, 496], "type": "text", "lines": [{"bbox": [501, 477, 867, 496], "spans": [{"bbox": [501, 477, 867, 496], "type": "text", "content": "建设能力实现设备集中纳管 数据集中增量价值挖掘运营"}]}], "index": 12}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 2}, {"para_blocks": [{"bbox": [66, 21, 527, 51], "type": "title", "lines": [{"bbox": [66, 21, 527, 51], "spans": [{"bbox": [66, 21, 527, 51], "type": "text", "content": "1、行业需求分析-酒店信息化服务需求迫切"}]}], "index": 0, "level": 1}, {"bbox": [24, 77, 952, 128], "type": "text", "lines": [{"bbox": [24, 77, 952, 128], "spans": [{"bbox": [24, 77, 952, 128], "type": "text", "content": "- 建设酒店Wi-Fi有助于满足客人需求、提高酒店竞争力、促进技术创新、优化运营成本、承担社会责任以及符合政策导向。因此，酒店有必要建设Wi-Fi服务。"}]}], "index": 1}, {"bbox": [186, 145, 306, 164], "type": "title", "lines": [{"bbox": [186, 145, 306, 164], "spans": [{"bbox": [186, 145, 306, 164], "type": "text", "content": "酒店WiFi覆盖痛点"}]}], "index": 2, "level": 1}, {"bbox": [666, 145, 755, 164], "type": "title", "lines": [{"bbox": [666, 145, 755, 164], "spans": [{"bbox": [666, 145, 755, 164], "type": "text", "content": "酒店组网需求"}]}], "index": 3, "level": 1}, {"bbox": [98, 196, 193, 210], "type": "title", "lines": [{"bbox": [98, 196, 193, 210], "spans": [{"bbox": [98, 196, 193, 210], "type": "text", "content": "长距离网线信号衰减"}]}], "index": 4, "level": 1}, {"bbox": [301, 195, 397, 208], "type": "title", "lines": [{"bbox": [301, 195, 397, 208], "spans": [{"bbox": [301, 195, 397, 208], "type": "text", "content": "WiFi漫游无法跨地域"}]}], "index": 5, "level": 1}, {"bbox": [60, 213, 232, 241], "type": "text", "lines": [{"bbox": [60, 213, 232, 241], "spans": [{"bbox": [60, 213, 232, 241], "type": "text", "content": "- 光纤到酒店，网线到房间，长距离网线传输导致网速衰减"}]}], "index": 6}, {"bbox": [261, 213, 395, 241], "type": "text", "lines": [{"bbox": [261, 213, 395, 241], "spans": [{"bbox": [261, 213, 395, 241], "type": "text", "content": "- 只支持单体酒店内WiFi漫游；- 连锁酒店跨区域需要二次登录"}]}], "index": 7}, {"type": "image", "bbox": [72, 244, 216, 308], "blocks": [{"bbox": [72, 244, 216, 308], "lines": [{"bbox": [72, 244, 216, 308], "spans": [{"bbox": [72, 244, 216, 308], "type": "image", "image_path": "422d85c62a180349a23fa29abfc97fe96fb5b9094d5bba176fc140b972bfba03.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}, {"type": "image", "bbox": [280, 244, 415, 308], "blocks": [{"bbox": [280, 244, 415, 308], "lines": [{"bbox": [280, 244, 415, 308], "spans": [{"bbox": [280, 244, 415, 308], "type": "image", "image_path": "0e1e0b1e9f783a71ab09f2e4d392dab40733eebe984f94495c8b4e4d78e02bf4.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [79, 325, 206, 338], "type": "title", "lines": [{"bbox": [79, 325, 206, 338], "spans": [{"bbox": [79, 325, 206, 338], "type": "text", "content": "WiFi漫游不支持跨设备形态"}]}], "index": 10, "level": 1}, {"bbox": [57, 343, 223, 370], "type": "text", "lines": [{"bbox": [57, 343, 223, 370], "spans": [{"bbox": [57, 343, 223, 370], "type": "text", "content": "- 路由器mesh组网、融合网关直接提供WiFi、AC+AP组网"}]}], "index": 11}, {"type": "image", "bbox": [74, 373, 211, 437], "blocks": [{"bbox": [74, 373, 211, 437], "lines": [{"bbox": [74, 373, 211, 437], "spans": [{"bbox": [74, 373, 211, 437], "type": "image", "image_path": "10942d1e2e4a9e2f90fd408857957217f6a6ad0c270fe33fb6a9af85dd2361c4.jpg"}]}], "index": 12, "type": "image_body"}], "index": 12}, {"bbox": [310, 325, 383, 338], "type": "title", "lines": [{"bbox": [310, 325, 383, 338], "spans": [{"bbox": [310, 325, 383, 338], "type": "text", "content": "投屏安全性不佳"}]}], "index": 13, "level": 1}, {"type": "image", "bbox": [261, 373, 415, 437], "blocks": [{"bbox": [261, 373, 415, 437], "lines": [{"bbox": [261, 373, 415, 437], "spans": [{"bbox": [261, 373, 415, 437], "type": "image", "image_path": "0115c59ed1060ad6b419e4a7bff4190af60af532a91d12beec5bff40e0d510dc.jpg"}]}], "index": 14, "type": "image_body"}], "index": 14}, {"type": "image", "bbox": [535, 218, 869, 414], "blocks": [{"bbox": [535, 218, 869, 414], "lines": [{"bbox": [535, 218, 869, 414], "spans": [{"bbox": [535, 218, 869, 414], "type": "image", "image_path": "2cb1d7070d686a96146fc01fb9ac5ee3b98453ea3ff4e7231be3b06aaa48c61c.jpg"}]}], "index": 15, "type": "image_body"}, {"bbox": [535, 187, 659, 205], "lines": [{"bbox": [535, 187, 659, 205], "spans": [{"bbox": [535, 187, 659, 205], "type": "text", "content": "酒店组网应用范围："}]}], "index": 16, "type": "image_caption"}], "index": 15}, {"bbox": [530, 440, 598, 455], "type": "title", "lines": [{"bbox": [530, 440, 598, 455], "spans": [{"bbox": [530, 440, 598, 455], "type": "text", "content": "客户需求："}]}], "index": 17, "level": 1}, {"bbox": [529, 460, 869, 497], "type": "text", "lines": [{"bbox": [529, 460, 869, 497], "spans": [{"bbox": [529, 460, 869, 497], "type": "text", "content": "- 客人流畅的无缝上网体验- 房间内智能设备联网- 公安对安全审计要求- 移动式服务设备联网"}]}], "index": 18}, {"bbox": [59, 452, 204, 504], "type": "text", "lines": [{"bbox": [59, 452, 204, 504], "spans": [{"bbox": [59, 452, 204, 504], "type": "text", "content": "酒店WiFi鉴权复杂，安全审计功能缺失- 多个认证系统需要鉴权登录操作"}]}], "index": 19}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 3}, {"para_blocks": [{"bbox": [64, 21, 432, 51], "type": "title", "lines": [{"bbox": [64, 21, 432, 51], "spans": [{"bbox": [64, 21, 432, 51], "type": "text", "content": "2、方案概述及功能-整体方案架构"}]}], "index": 0, "level": 1}, {"bbox": [31, 77, 930, 126], "type": "text", "lines": [{"bbox": [31, 77, 930, 126], "spans": [{"bbox": [31, 77, 930, 126], "type": "text", "content": "- 轻量化智慧酒店解决方案为客户提供原子化服务和应用，基于SaaS化云原生搭建，切入酒店客户日常生产，以提供PMS服务、IOT设备管理服务、组网增值服务为核心，形成酒店智慧化完整解决方案，实现轻量化定制的一站式交付。"}]}], "index": 1}, {"type": "image", "bbox": [40, 178, 906, 489], "blocks": [{"bbox": [40, 178, 906, 489], "lines": [{"bbox": [40, 178, 906, 489], "spans": [{"bbox": [40, 178, 906, 489], "type": "image", "image_path": "88e2273781337009eac882e12fa794026d4ec976b92d53bf6d49a147213b232c.jpg"}]}], "index": 2, "type": "image_body"}, {"bbox": [330, 149, 612, 171], "lines": [{"bbox": [330, 149, 612, 171], "spans": [{"bbox": [330, 149, 612, 171], "type": "text", "content": "泛住宿场景轻量化智慧酒店方案全景图"}]}], "index": 3, "type": "image_caption"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 4}, {"para_blocks": [{"bbox": [64, 21, 633, 51], "type": "title", "lines": [{"bbox": [64, 21, 633, 51], "spans": [{"bbox": [64, 21, 633, 51], "type": "text", "content": "2、方案概述及功能-看平台（酒店IOT设备管理平台）"}]}], "index": 0, "level": 1}, {"bbox": [53, 73, 823, 124], "type": "text", "lines": [{"bbox": [53, 73, 823, 124], "spans": [{"bbox": [53, 73, 823, 124], "type": "text", "content": "统一管理无线网络、监控安消、门锁、送物机器人、智能客控等设备，一平台解决多平台操作。联动酒店全量数据，衍生能耗管理、飞房管理、客房服务等应用，解决酒店人员管理及运营环节效率低痛点。"}]}], "index": 1}, {"bbox": [350, 175, 461, 193], "type": "title", "lines": [{"bbox": [350, 175, 461, 193], "spans": [{"bbox": [350, 175, 461, 193], "type": "text", "content": "IoT设备管理能力"}]}], "index": 2, "level": 1}, {"bbox": [511, 162, 902, 233], "type": "text", "lines": [{"bbox": [511, 162, 902, 233], "spans": [{"bbox": [511, 162, 902, 233], "type": "text", "content": "设备管理 实现客控设备、门锁、机器人、监控安消等设备的接入和管理  运维管理 智能化设备运维，故障告警生成工单快速通知维修，形成闭环  场景联动 支持智能设备场景模板管理，实现设备互联互通和联动  装维管理 提供装维侧工单、人员、设备升级管理，更加高效省心"}]}], "index": 3}, {"type": "image", "bbox": [86, 225, 314, 352], "blocks": [{"bbox": [86, 225, 314, 352], "lines": [{"bbox": [86, 225, 314, 352], "spans": [{"bbox": [86, 225, 314, 352], "type": "image", "image_path": "801ca0988d14f08f3a1056c4df8e3f0dabcea85c50513e190bd10874d2f1e130.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"bbox": [357, 360, 445, 378], "type": "title", "lines": [{"bbox": [357, 360, 445, 378], "spans": [{"bbox": [357, 360, 445, 378], "type": "text", "content": "酒店应用服务"}]}], "index": 5, "level": 1}, {"bbox": [511, 295, 907, 457], "type": "text", "lines": [{"bbox": [511, 295, 907, 457], "spans": [{"bbox": [511, 295, 907, 457], "type": "text", "content": "权限管理 对不同角色设置相应的管理权限，支持账号的增查删改  飞房管理 通过用电情况、智能门锁状态等IoT数据经数据分析识别实际入住人员身份及入住状态，为酒店业主的“飞房”痛点  能耗管理 通过智能控制设备+酒店运营策略，提供能耗统计、能耗使用情况分析等综合管理能力  客房服务 提供对客需服务的实时接收和快速响应，提升宾客入住质量和体验  无线管理 统一配置管理，实现酒店内全区域的Wi- Fi覆盖，提升用户网络体验"}]}], "index": 6}, {"bbox": [291, 483, 655, 509], "type": "title", "lines": [{"bbox": [291, 483, 655, 509], "spans": [{"bbox": [291, 483, 655, 509], "type": "text", "content": "设备统一管理，解决酒店管理及运营痛点"}]}], "index": 7, "level": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 5}, {"para_blocks": [{"bbox": [64, 22, 501, 51], "type": "title", "lines": [{"bbox": [64, 22, 501, 51], "spans": [{"bbox": [64, 22, 501, 51], "type": "text", "content": "2、方案概述及功能-看平台（PMS平台）"}]}], "index": 0, "level": 1}, {"bbox": [47, 65, 895, 125], "type": "text", "lines": [{"bbox": [47, 65, 895, 125], "spans": [{"bbox": [47, 65, 895, 125], "type": "text", "content": "PMS平台用于管理酒店的日常业务，包括预订管理、渠道管理、房态管理、客户服务、报表管理、会员管理等涵盖住前、住中和住后阶段的全方位服务。通过PMS平台，酒店员工可以轻松地管理多个业务，实现全面的客户关系管理，提高操作效率，加强客户服务，提升酒店形象和声誉，并为酒店品牌增值。"}]}], "index": 1}, {"type": "image", "bbox": [69, 225, 299, 400], "blocks": [{"bbox": [69, 225, 299, 400], "lines": [{"bbox": [69, 225, 299, 400], "spans": [{"bbox": [69, 225, 299, 400], "type": "image", "image_path": "d1f94e5512061f3fafc60db05a5b3ebb83bb7c6828c211bd28bfe81eeb8efc21.jpg"}]}], "index": 2, "type": "image_body"}, {"bbox": [325, 182, 445, 201], "lines": [{"bbox": [325, 182, 445, 201], "spans": [{"bbox": [325, 182, 445, 201], "type": "text", "content": "PMS平台基础能力"}]}], "index": 3, "type": "image_caption"}], "index": 2}, {"bbox": [496, 139, 873, 265], "type": "text", "lines": [{"bbox": [496, 139, 873, 265], "spans": [{"bbox": [496, 139, 873, 265], "type": "text", "content": "前台接待 集合预订、接待、收银于一体，支持移动支付  销售管理 客户资料记录、分析，全面掌控和提升客户关系，打造客户资源拓展中心  财务管理 支持应收帐管理、一键自动夜审工作，严谨与灵活兼备  客房管理 房间状态管理专家，实时监控房态，快速响应客人服务需求  报表管理 系统自带多种模块报表，您还可按需自行制定专属报表  客史管理 支持添加客人偏好记录，VIP等级、历史消费记录、积分"}]}], "index": 4}, {"bbox": [552, 307, 799, 324], "type": "text", "lines": [{"bbox": [552, 307, 799, 324], "spans": [{"bbox": [552, 307, 799, 324], "type": "text", "content": "携程、美团、飞猪一键直连，订单、房价可控"}]}], "index": 5}, {"type": "image", "bbox": [624, 330, 749, 448], "blocks": [{"bbox": [624, 330, 749, 448], "lines": [{"bbox": [624, 330, 749, 448], "spans": [{"bbox": [624, 330, 749, 448], "type": "image", "image_path": "54b0ce83b24f8e8a6408d9940ff2c1f987d33dfd1c36bdc334a687c83091e5e3.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}, {"bbox": [249, 483, 701, 509], "type": "text", "lines": [{"bbox": [249, 483, 701, 509], "spans": [{"bbox": [249, 483, 701, 509], "type": "text", "content": "高性价比PMS平台，覆盖酒店前台接待管理全流程"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 6}, {"para_blocks": [{"bbox": [64, 21, 585, 51], "type": "title", "lines": [{"bbox": [64, 21, 585, 51], "spans": [{"bbox": [64, 21, 585, 51], "type": "text", "content": "2、方案概述及功能-看平台（移动端IOT+PMS）"}]}], "index": 0, "level": 1}, {"type": "image", "bbox": [0, 69, 954, 504], "blocks": [{"bbox": [0, 69, 954, 504], "lines": [{"bbox": [0, 69, 954, 504], "spans": [{"bbox": [0, 69, 954, 504], "type": "image", "image_path": "bf476c60f02cc356106dee98c34405a336eae2d27c8d5d50880ccc0b1b1b3db2.jpg"}]}], "index": 1, "type": "image_body"}], "index": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 7}, {"para_blocks": [{"bbox": [64, 21, 566, 51], "type": "title", "lines": [{"bbox": [64, 21, 566, 51], "spans": [{"bbox": [64, 21, 566, 51], "type": "text", "content": "2、方案概述及功能-看功能（智能设备及客控）"}]}], "index": 0, "level": 1}, {"bbox": [317, 74, 634, 102], "type": "title", "lines": [{"bbox": [317, 74, 634, 102], "spans": [{"bbox": [317, 74, 634, 102], "type": "text", "content": "智能化客房，入住体验快速提升"}]}], "index": 1, "level": 1}, {"bbox": [67, 111, 280, 140], "type": "title", "lines": [{"bbox": [67, 111, 280, 140], "spans": [{"bbox": [67, 111, 280, 140], "type": "text", "content": "智慧客房·方案功能"}]}], "index": 2, "level": 1}, {"bbox": [86, 166, 141, 183], "type": "title", "lines": [{"bbox": [86, 166, 141, 183], "spans": [{"bbox": [86, 166, 141, 183], "type": "text", "content": "灯光控制"}]}], "index": 3, "level": 1}, {"bbox": [241, 166, 293, 183], "type": "title", "lines": [{"bbox": [241, 166, 293, 183], "spans": [{"bbox": [241, 166, 293, 183], "type": "text", "content": "语音控制"}]}], "index": 4, "level": 1}, {"bbox": [392, 166, 447, 183], "type": "title", "lines": [{"bbox": [392, 166, 447, 183], "spans": [{"bbox": [392, 166, 447, 183], "type": "text", "content": "情景控制"}]}], "index": 5, "level": 1}, {"bbox": [549, 166, 599, 183], "type": "title", "lines": [{"bbox": [549, 166, 599, 183], "spans": [{"bbox": [549, 166, 599, 183], "type": "text", "content": "通知控制"}]}], "index": 6, "level": 1}, {"type": "image", "bbox": [679, 132, 923, 261], "blocks": [{"bbox": [679, 132, 923, 261], "lines": [{"bbox": [679, 132, 923, 261], "spans": [{"bbox": [679, 132, 923, 261], "type": "image", "image_path": "231a8884952c7a07ca42003c0d0ef039bc5e6d955a4a2aac39de194cf4778f53.jpg"}]}], "index": 7, "type": "image_body"}, {"bbox": [743, 266, 879, 284], "lines": [{"bbox": [743, 266, 879, 284], "spans": [{"bbox": [743, 266, 879, 284], "type": "text", "content": "智慧酒店loT管理平台"}]}], "index": 8, "type": "image_caption"}], "index": 7}, {"type": "image", "bbox": [47, 196, 184, 273], "blocks": [{"bbox": [47, 196, 184, 273], "lines": [{"bbox": [47, 196, 184, 273], "spans": [{"bbox": [47, 196, 184, 273], "type": "image", "image_path": "f0ec8812746cde40970dabd4c0fb5ab02153de798e9977e000ed419186bcbb92.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [49, 287, 173, 328], "type": "text", "lines": [{"bbox": [49, 287, 173, 328], "spans": [{"bbox": [49, 287, 173, 328], "type": "text", "content": "可通过按键、语音、小程序控制灯光，也可实现场景联动，自动亮灯，如门开灯亮等"}]}], "index": 10}, {"type": "image", "bbox": [199, 195, 335, 273], "blocks": [{"bbox": [199, 195, 335, 273], "lines": [{"bbox": [199, 195, 335, 273], "spans": [{"bbox": [199, 195, 335, 273], "type": "image", "image_path": "3b86cbdb5026562bfc0465e3f6975b2a0ba966482fead08c8492907588b022a8.jpg"}]}], "index": 11, "type": "image_body"}], "index": 11}, {"bbox": [203, 287, 325, 328], "type": "text", "lines": [{"bbox": [203, 287, 325, 328], "spans": [{"bbox": [203, 287, 325, 328], "type": "text", "content": "智能语音对话，可实现语音控制客房内智能电器，包括灯光、空调、窗帘、电视等"}]}], "index": 12}, {"type": "image", "bbox": [350, 195, 485, 273], "blocks": [{"bbox": [350, 195, 485, 273], "lines": [{"bbox": [350, 195, 485, 273], "spans": [{"bbox": [350, 195, 485, 273], "type": "image", "image_path": "f1bee15de2466f864f40a9af031d56bb87e9ff07ec0e37601d2944b990801614.jpg"}]}], "index": 13, "type": "image_body"}], "index": 13}, {"bbox": [354, 287, 480, 328], "type": "text", "lines": [{"bbox": [354, 287, 480, 328], "spans": [{"bbox": [354, 287, 480, 328], "type": "text", "content": "可后台设定情景模式，如客房灯光的起床模式、睡眠模式、柔和模式、阅读模式等"}]}], "index": 14}, {"type": "image", "bbox": [505, 195, 638, 273], "blocks": [{"bbox": [505, 195, 638, 273], "lines": [{"bbox": [505, 195, 638, 273], "spans": [{"bbox": [505, 195, 638, 273], "type": "image", "image_path": "c2a5a7e7ea81cfd6e05d45ee98fac8511dbff4d0907b8e90d2227960b667b8dc.jpg"}]}], "index": 15, "type": "image_body"}], "index": 15}, {"bbox": [506, 294, 631, 321], "type": "text", "lines": [{"bbox": [506, 294, 631, 321], "spans": [{"bbox": [506, 294, 631, 321], "type": "text", "content": "可通过按键、语音、小程序控制窗帘，可实现窗纱、窗帘分别控制"}]}], "index": 16}, {"type": "image", "bbox": [707, 300, 834, 342], "blocks": [{"bbox": [707, 300, 834, 342], "lines": [{"bbox": [707, 300, 834, 342], "spans": [{"bbox": [707, 300, 834, 342], "type": "image", "image_path": "7af7d4267ac1f0b0fa97e4494b6ac4ba5c8f53d5a4eef1c309920a7045d03a95.jpg"}]}], "index": 17, "type": "image_body"}, {"bbox": [714, 343, 751, 357], "lines": [{"bbox": [714, 343, 751, 357], "spans": [{"bbox": [714, 343, 751, 357], "type": "text", "content": "取电网关"}]}], "index": 18, "type": "image_caption"}, {"bbox": [775, 343, 832, 357], "lines": [{"bbox": [775, 343, 832, 357], "spans": [{"bbox": [775, 343, 832, 357], "type": "text", "content": "智能面板"}]}], "index": 24, "type": "image_caption"}], "index": 17}, {"bbox": [88, 360, 140, 376], "type": "title", "lines": [{"bbox": [88, 360, 140, 376], "spans": [{"bbox": [88, 360, 140, 376], "type": "text", "content": "空调控制"}]}], "index": 19, "level": 1}, {"bbox": [241, 360, 293, 376], "type": "title", "lines": [{"bbox": [241, 360, 293, 376], "spans": [{"bbox": [241, 360, 293, 376], "type": "text", "content": "影音控制"}]}], "index": 20, "level": 1}, {"bbox": [392, 360, 446, 376], "type": "title", "lines": [{"bbox": [392, 360, 446, 376], "spans": [{"bbox": [392, 360, 446, 376], "type": "text", "content": "前台呼叫"}]}], "index": 21, "level": 1}, {"bbox": [549, 360, 599, 376], "type": "title", "lines": [{"bbox": [549, 360, 599, 376], "spans": [{"bbox": [549, 360, 599, 376], "type": "text", "content": "智能取电"}]}], "index": 22, "level": 1}, {"type": "image", "bbox": [712, 369, 840, 483], "blocks": [{"bbox": [712, 369, 840, 483], "lines": [{"bbox": [712, 369, 840, 483], "spans": [{"bbox": [712, 369, 840, 483], "type": "image", "image_path": "7b60a13fa05cdf0212b7c5b5792693dd93d15238d42b71d9eaca0ce33254ffef.jpg"}]}], "index": 23, "type": "image_body"}], "index": 23}, {"type": "image", "bbox": [48, 392, 181, 463], "blocks": [{"bbox": [48, 392, 181, 463], "lines": [{"bbox": [48, 392, 181, 463], "spans": [{"bbox": [48, 392, 181, 463], "type": "image", "image_path": "f5108cb7b5b5d8849c4f7edb535dc7afe9c23c127854bbc754394a4a7ee9d8c4.jpg"}]}], "index": 25, "type": "image_body"}], "index": 25}, {"bbox": [49, 480, 173, 517], "type": "text", "lines": [{"bbox": [49, 480, 173, 517], "spans": [{"bbox": [49, 480, 173, 517], "type": "text", "content": "可通过按键、语音、小程序控制客房空调，可定时、可远程打开空调，控制模式、风速、温度等"}]}], "index": 26}, {"type": "image", "bbox": [199, 391, 336, 463], "blocks": [{"bbox": [199, 391, 336, 463], "lines": [{"bbox": [199, 391, 336, 463], "spans": [{"bbox": [199, 391, 336, 463], "type": "image", "image_path": "c985678bb83a49110888fce8874ee4316ecbe252f4c171692c70b85ed5f3f7df.jpg"}]}], "index": 27, "type": "image_body"}], "index": 27}, {"bbox": [203, 480, 325, 517], "type": "text", "lines": [{"bbox": [203, 480, 325, 517], "spans": [{"bbox": [203, 480, 325, 517], "type": "text", "content": "可通过语音控制客房电视操作，打开电视、遥控阳台、音量控制，免去寻找遥控器的烦恼"}]}], "index": 28}, {"type": "image", "bbox": [350, 391, 485, 466], "blocks": [{"bbox": [350, 391, 485, 466], "lines": [{"bbox": [350, 391, 485, 466], "spans": [{"bbox": [350, 391, 485, 466], "type": "image", "image_path": "5178cf8a0bf8ffe54f33036c4850d4cf6b69f081a70db98f7bb79be0d2a77248.jpg"}]}], "index": 29, "type": "image_body"}], "index": 29}, {"type": "image", "bbox": [506, 391, 637, 463], "blocks": [{"bbox": [506, 391, 637, 463], "lines": [{"bbox": [506, 391, 637, 463], "spans": [{"bbox": [506, 391, 637, 463], "type": "image", "image_path": "22fa8837d2a5950c8b2dac97b598980d75f628ae3e5d7656033167f729f43c9a.jpg"}]}], "index": 30, "type": "image_body"}], "index": 30}, {"bbox": [506, 485, 626, 511], "type": "text", "lines": [{"bbox": [506, 485, 626, 511], "spans": [{"bbox": [506, 485, 626, 511], "type": "text", "content": "插卡后智能取电，执行欢迎模式；拔卡后智能下电，避免能耗浪费"}]}], "index": 31}, {"bbox": [743, 489, 790, 502], "type": "text", "lines": [{"bbox": [743, 489, 790, 502], "spans": [{"bbox": [743, 489, 790, 502], "type": "text", "content": "智能音箱"}]}], "index": 32}, {"type": "image", "bbox": [880, 372, 912, 432], "blocks": [{"bbox": [880, 372, 912, 432], "lines": [{"bbox": [880, 372, 912, 432], "spans": [{"bbox": [880, 372, 912, 432], "type": "image", "image_path": "e7e89acdb06765d64efd4402af502b76c1afb4afadbcf524ced295622f2dcf0f.jpg"}]}], "index": 33, "type": "image_body"}], "index": 33}, {"type": "image", "bbox": [880, 440, 912, 481], "blocks": [{"bbox": [880, 440, 912, 481], "lines": [{"bbox": [880, 440, 912, 481], "spans": [{"bbox": [880, 440, 912, 481], "type": "image", "image_path": "0f16dcd77b02d7ab2ece5d71fa710f961a733a7ea04d3984b2090615c4a09371.jpg"}]}], "index": 34, "type": "image_body"}], "index": 34}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 8}, {"para_blocks": [{"bbox": [64, 22, 566, 51], "type": "title", "lines": [{"bbox": [64, 22, 566, 51], "spans": [{"bbox": [64, 22, 566, 51], "type": "text", "content": "2、方案概述及功能-看功能（智能设备及客控）"}]}], "index": 0, "level": 1}, {"bbox": [314, 69, 631, 96], "type": "title", "lines": [{"bbox": [314, 69, 631, 96], "spans": [{"bbox": [314, 69, 631, 96], "type": "text", "content": "送物机器人，智慧服务省时省力"}]}], "index": 1, "level": 1}, {"bbox": [24, 121, 148, 147], "type": "title", "lines": [{"bbox": [24, 121, 148, 147], "spans": [{"bbox": [24, 121, 148, 147], "type": "text", "content": "双仓大容量"}]}], "index": 2, "level": 1}, {"bbox": [293, 121, 392, 147], "type": "title", "lines": [{"bbox": [293, 121, 392, 147], "spans": [{"bbox": [293, 121, 392, 147], "type": "text", "content": "流线机身"}]}], "index": 3, "level": 1}, {"bbox": [638, 120, 890, 178], "type": "title", "lines": [{"bbox": [638, 120, 890, 178], "spans": [{"bbox": [638, 120, 890, 178], "type": "text", "content": "可搭配智能货柜服务7*24小时无人售货+配送"}]}], "index": 4, "level": 1}, {"bbox": [23, 154, 226, 171], "type": "text", "lines": [{"bbox": [23, 154, 226, 171], "spans": [{"bbox": [23, 154, 226, 171], "type": "text", "content": "45L/层大舱室，独有无异味残留设计"}]}], "index": 5}, {"bbox": [288, 154, 548, 171], "type": "text", "lines": [{"bbox": [288, 154, 548, 171], "spans": [{"bbox": [288, 154, 548, 171], "type": "text", "content": "流线型机身，优雅外观，提升酒店/楼宇品质感"}]}], "index": 6}, {"bbox": [27, 183, 160, 210], "type": "title", "lines": [{"bbox": [27, 183, 160, 210], "spans": [{"bbox": [27, 183, 160, 210], "type": "text", "content": "3D智能避障"}]}], "index": 7, "level": 1}, {"bbox": [293, 183, 393, 210], "type": "title", "lines": [{"bbox": [293, 183, 393, 210], "spans": [{"bbox": [293, 183, 393, 210], "type": "text", "content": "高清大屏"}]}], "index": 8, "level": 1}, {"bbox": [647, 192, 881, 212], "type": "text", "lines": [{"bbox": [647, 192, 881, 212], "spans": [{"bbox": [647, 192, 881, 212], "type": "text", "content": "房间内自助下单，机器人取货、送货"}]}], "index": 9}, {"bbox": [23, 213, 282, 244], "type": "text", "lines": [{"bbox": [23, 213, 282, 244], "spans": [{"bbox": [23, 213, 282, 244], "type": "text", "content": "双目立体视觉避障系统，轻松应对复杂环境流线型机身，优雅外观，提升酒店/楼宇品质感"}]}], "index": 10}, {"bbox": [290, 213, 527, 230], "type": "text", "lines": [{"bbox": [290, 213, 527, 230], "spans": [{"bbox": [290, 213, 527, 230], "type": "text", "content": "11.6寸大屏，可迎宾+广告，营销口碑利器"}]}], "index": 11}, {"type": "image", "bbox": [120, 270, 389, 517], "blocks": [{"bbox": [120, 270, 389, 517], "lines": [{"bbox": [120, 270, 389, 517], "spans": [{"bbox": [120, 270, 389, 517], "type": "image", "image_path": "e13ab56beb8568f46ae66ff269ebdf95311711bff98104cc16a25ac302fc9552.jpg"}]}], "index": 12, "type": "image_body"}], "index": 12}, {"type": "image", "bbox": [616, 269, 865, 516], "blocks": [{"bbox": [616, 269, 865, 516], "lines": [{"bbox": [616, 269, 865, 516], "spans": [{"bbox": [616, 269, 865, 516], "type": "image", "image_path": "4a8371c9f3cb17850c3dcb5722d14fe65c3cec0352d225dd8b2e8df3301267db.jpg"}]}], "index": 13, "type": "image_body"}], "index": 13}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 9}, {"para_blocks": [{"bbox": [64, 21, 566, 51], "type": "title", "lines": [{"bbox": [64, 21, 566, 51], "spans": [{"bbox": [64, 21, 566, 51], "type": "text", "content": "2、方案概述及功能-看功能（智能设备及客控）"}]}], "index": 0, "level": 1}, {"bbox": [325, 96, 635, 123], "type": "title", "lines": [{"bbox": [325, 96, 635, 123], "spans": [{"bbox": [325, 96, 635, 123], "type": "text", "content": "入住一体机，30s快速入住办理"}]}], "index": 1, "level": 1}, {"type": "image", "bbox": [31, 159, 197, 285], "blocks": [{"bbox": [31, 159, 197, 285], "lines": [{"bbox": [31, 159, 197, 285], "spans": [{"bbox": [31, 159, 197, 285], "type": "image", "image_path": "890537e177ec409afd074980173dccfcb9ba00b515ac0a9a5b054f4d473bdae7.jpg"}]}], "index": 2, "type": "image_body"}, {"bbox": [81, 293, 145, 307], "lines": [{"bbox": [81, 293, 145, 307], "spans": [{"bbox": [81, 293, 145, 307], "type": "text", "content": "一体机台式版"}]}], "index": 3, "type": "image_caption"}], "index": 2}, {"bbox": [213, 155, 448, 279], "type": "text", "lines": [{"bbox": [213, 155, 448, 279], "spans": [{"bbox": [213, 155, 448, 279], "type": "text", "content": "- 主屏15.6寸+副屏15.6寸- 430mm*382mm*215mm（长*宽*高）- CPUkyro8核2.2G，内存4G，存储64G- 以太网，USB和WiFi- 热敏小票打印机（选配）- 200W高清数码摄像头- 二代证读卡器，符合公安部GA450/IG450标准"}]}], "index": 4}, {"bbox": [232, 325, 312, 339], "type": "text", "lines": [{"bbox": [232, 325, 312, 339], "spans": [{"bbox": [232, 325, 312, 339], "type": "text", "content": "24寸液晶显示器"}]}], "index": 5}, {"bbox": [213, 343, 472, 483], "type": "text", "lines": [{"bbox": [213, 343, 472, 483], "spans": [{"bbox": [213, 343, 472, 483], "type": "text", "content": "- 540mm*490mm*1810mm（长*宽*高）- CPURK3399六核频率1.8GHz，内存4G，存储16G- Android/windows双系统- 二代证读卡器，符合公安部GA450/IG450标准- 自动循环发卡机- 80mm热敏小票打印机- 3D结构光高清活体摄像头- 二维码扫描仪"}]}], "index": 6}, {"bbox": [516, 189, 911, 207], "type": "text", "lines": [{"bbox": [516, 189, 911, 207], "spans": [{"bbox": [516, 189, 911, 207], "type": "text", "content": "- 服务质量 服务范围 服务体验 会员服务 销售服务 城市服务"}]}], "index": 7}, {"type": "image", "bbox": [501, 229, 909, 412], "blocks": [{"bbox": [501, 229, 909, 412], "lines": [{"bbox": [501, 229, 909, 412], "spans": [{"bbox": [501, 229, 909, 412], "type": "image", "image_path": "ebd49242dce40e47027d88aeb77e0c65ecec9adbda00b6ba05167d45e8c6b363.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}, {"bbox": [576, 444, 871, 463], "type": "text", "lines": [{"bbox": [576, 444, 871, 463], "spans": [{"bbox": [576, 444, 871, 463], "type": "text", "content": "支持支付宝、微信、授权支付、刷脸支付"}]}], "index": 9}, {"type": "image", "bbox": [44, 324, 184, 463], "blocks": [{"bbox": [44, 324, 184, 463], "lines": [{"bbox": [44, 324, 184, 463], "spans": [{"bbox": [44, 324, 184, 463], "type": "image", "image_path": "c4fbc92eb6244eb0420a84a04fb3418e0cd8f4d48b773d65503f1bf00545d516.jpg"}]}], "index": 10, "type": "image_body"}, {"bbox": [81, 480, 145, 494], "lines": [{"bbox": [81, 480, 145, 494], "spans": [{"bbox": [81, 480, 145, 494], "type": "text", "content": "一体机手持版"}]}], "index": 11, "type": "image_caption"}], "index": 10}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 10}, {"para_blocks": [{"bbox": [64, 21, 568, 51], "type": "title", "lines": [{"bbox": [64, 21, 568, 51], "spans": [{"bbox": [64, 21, 568, 51], "type": "text", "content": "2、方案概述及功能-看功能（智能设备及客控）"}]}], "index": 0, "level": 1}, {"type": "image", "bbox": [18, 78, 954, 534], "blocks": [{"bbox": [18, 78, 954, 534], "lines": [{"bbox": [18, 78, 954, 534], "spans": [{"bbox": [18, 78, 954, 534], "type": "image", "image_path": "36f1bcff35441ee361f7775d89b6ecc17c3d35762c702fb86b94199617057f15.jpg"}]}], "index": 1, "type": "image_body"}], "index": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 11}, {"para_blocks": [{"bbox": [64, 22, 550, 51], "type": "title", "lines": [{"bbox": [64, 22, 550, 51], "spans": [{"bbox": [64, 22, 550, 51], "type": "text", "content": "2、方案概述及功能-看功能（WIFI漫游管理）"}]}], "index": 0, "level": 1}, {"bbox": [24, 76, 952, 122], "type": "text", "lines": [{"bbox": [24, 76, 952, 122], "spans": [{"bbox": [24, 76, 952, 122], "type": "text", "content": "- 组网WiFi漫游是一套支持多种灵活组网架构产品，基于传统组网硬件，通过给组网硬件安装blink插件+云平台统一管理方式，实现具备更高性价比的类AC+AP组网效果。针对酒店场景，房间内融合网关、公区AX18覆盖的轻量化WiFi漫游覆盖。"}]}], "index": 1}, {"type": "image", "bbox": [66, 135, 504, 471], "blocks": [{"bbox": [66, 135, 504, 471], "lines": [{"bbox": [66, 135, 504, 471], "spans": [{"bbox": [66, 135, 504, 471], "type": "image", "image_path": "7fe8b8de64b6715e28350537d39265e24aaafce6639c1c6b4ff5e90ecf6cbcfb.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}, {"type": "image", "bbox": [605, 184, 906, 376], "blocks": [{"bbox": [605, 184, 906, 376], "lines": [{"bbox": [605, 184, 906, 376], "spans": [{"bbox": [605, 184, 906, 376], "type": "image", "image_path": "37b7b0224e9885d0a60d99a2d97148c8c9c2b1fb123ccfcb9ed6139c7b10f1b3.jpg"}]}], "index": 4, "type": "image_body"}, {"bbox": [607, 145, 792, 167], "lines": [{"bbox": [607, 145, 792, 167], "spans": [{"bbox": [607, 145, 792, 167], "type": "text", "content": "产品体系"}]}], "index": 3, "type": "image_caption"}], "index": 4}, {"bbox": [612, 402, 679, 419], "type": "title", "lines": [{"bbox": [612, 402, 679, 419], "spans": [{"bbox": [612, 402, 679, 419], "type": "text", "content": "产品优势："}]}], "index": 5, "level": 1}, {"bbox": [612, 424, 792, 494], "type": "text", "lines": [{"bbox": [612, 424, 792, 494], "spans": [{"bbox": [612, 424, 792, 494], "type": "text", "content": "- 无需改变现有组网架构；- 跨融合网关漫游；- 灵活扩容；- 性价比高（与AC+AP硬件组网相比）"}]}], "index": 6}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 12}, {"para_blocks": [{"bbox": [64, 21, 551, 51], "type": "title", "lines": [{"bbox": [64, 21, 551, 51], "spans": [{"bbox": [64, 21, 551, 51], "type": "text", "content": "2、方案概述及功能-看功能（WIFI漫游管理）"}]}], "index": 0, "level": 1}, {"bbox": [26, 76, 948, 122], "type": "text", "lines": [{"bbox": [26, 76, 948, 122], "spans": [{"bbox": [26, 76, 948, 122], "type": "text", "content": "- 主要功能包括：无限灵活扩容、portal定制、跨区域免认证登录、跨设备形态WiFi漫游、安全投屏、融合鉴权为核心差异化能力，为不同客户提供多价格区间的组网漫游选择。"}]}], "index": 1}, {"bbox": [429, 156, 524, 178], "type": "title", "lines": [{"bbox": [429, 156, 524, 178], "spans": [{"bbox": [429, 156, 524, 178], "type": "text", "content": "能力总视图"}]}], "index": 2, "level": 1}, {"bbox": [93, 210, 219, 229], "type": "title", "lines": [{"bbox": [93, 210, 219, 229], "spans": [{"bbox": [93, 210, 219, 229], "type": "text", "content": "常规酒店WiFi漫游"}]}], "index": 3, "level": 1}, {"bbox": [499, 208, 680, 227], "type": "title", "lines": [{"bbox": [499, 208, 680, 227], "spans": [{"bbox": [499, 208, 680, 227], "type": "text", "content": "Blink控制下的酒店WiFi漫游"}]}], "index": 4, "level": 1}, {"bbox": [98, 244, 221, 284], "type": "text", "lines": [{"bbox": [98, 244, 221, 284], "spans": [{"bbox": [98, 244, 221, 284], "type": "text", "content": "基于AC+AP的公区漫游"}]}], "index": 5}, {"bbox": [97, 298, 223, 338], "type": "text", "lines": [{"bbox": [97, 298, 223, 338], "spans": [{"bbox": [97, 298, 223, 338], "type": "text", "content": "同酒店范围内免认证登录"}]}], "index": 6}, {"bbox": [96, 365, 225, 381], "type": "text", "lines": [{"bbox": [96, 365, 225, 381], "spans": [{"bbox": [96, 365, 225, 381], "type": "text", "content": "投屏WiFi容易连接错误"}]}], "index": 7}, {"bbox": [96, 414, 221, 434], "type": "text", "lines": [{"bbox": [96, 414, 221, 434], "spans": [{"bbox": [96, 414, 221, 434], "type": "text", "content": "安审平台进行认证登录"}]}], "index": 8}, {"bbox": [101, 469, 217, 486], "type": "text", "lines": [{"bbox": [101, 469, 217, 486], "spans": [{"bbox": [101, 469, 217, 486], "type": "text", "content": "简易常规登录portal"}]}], "index": 9}, {"bbox": [328, 240, 498, 286], "type": "text", "lines": [{"bbox": [328, 240, 498, 286], "spans": [{"bbox": [328, 240, 498, 286], "type": "text", "content": "跨设备形态全域WiFi漫游融合网关、企业路由器、ACAP、FTTR之间跨设备形态漫游"}]}], "index": 10}, {"bbox": [326, 295, 508, 339], "type": "text", "lines": [{"bbox": [326, 295, 508, 339], "spans": [{"bbox": [326, 295, 508, 339], "type": "text", "content": "跨区域免认证登录管理酒店内跨区域免认证、跨酒店免认证"}]}], "index": 11}, {"bbox": [327, 349, 509, 394], "type": "text", "lines": [{"bbox": [327, 349, 509, 394], "spans": [{"bbox": [327, 349, 509, 394], "type": "text", "content": "客房投屏WiFi投屏WiFi实时开启、手机扫码自动连接"}]}], "index": 12}, {"bbox": [327, 401, 509, 446], "type": "text", "lines": [{"bbox": [327, 401, 509, 446], "spans": [{"bbox": [327, 401, 509, 446], "type": "text", "content": "统一认证，融合鉴权WiFi统一登录认证、鉴权结果安审复用"}]}], "index": 13}, {"bbox": [362, 460, 474, 490], "type": "text", "lines": [{"bbox": [362, 460, 474, 490], "spans": [{"bbox": [362, 460, 474, 490], "type": "text", "content": "品牌宣传portal模板选择，样式定制"}]}], "index": 14}, {"bbox": [528, 247, 852, 280], "type": "text", "lines": [{"bbox": [528, 247, 852, 280], "spans": [{"bbox": [528, 247, 852, 280], "type": "text", "content": "同酒店房间内、公区的跨设备形态WiFi漫游。支持企业路由器、ACAP、FTTR主从、融合网关混搭"}]}], "index": 15}, {"bbox": [528, 301, 860, 334], "type": "text", "lines": [{"bbox": [528, 301, 860, 334], "spans": [{"bbox": [528, 301, 860, 334], "type": "text", "content": "同品牌多区域酒店，用户设备登录过一次后，跨区域再次连接无需重复验证，提升用户使用体验"}]}], "index": 16}, {"bbox": [528, 355, 852, 388], "type": "text", "lines": [{"bbox": [528, 355, 852, 388], "spans": [{"bbox": [528, 355, 852, 388], "type": "text", "content": "手机扫描二维码开启并连接WiFi，解决投屏WiFi存在的误连、误投问题"}]}], "index": 17}, {"bbox": [528, 405, 852, 439], "type": "text", "lines": [{"bbox": [528, 405, 852, 439], "spans": [{"bbox": [528, 405, 852, 439], "type": "text", "content": "WiFi漫游平台认证登录鉴权结果安审复用，解决安审认证与WiFi漫游认证冲突问题"}]}], "index": 18}, {"bbox": [528, 457, 860, 490], "type": "text", "lines": [{"bbox": [528, 457, 860, 490], "spans": [{"bbox": [528, 457, 860, 490], "type": "text", "content": "对登录页背景、登录页布局、登录框样式、外部跳转链接等做灵活配置，预置标准模板"}]}], "index": 19}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 13}, {"para_blocks": [{"bbox": [64, 21, 549, 51], "type": "title", "lines": [{"bbox": [64, 21, 549, 51], "spans": [{"bbox": [64, 21, 549, 51], "type": "text", "content": "2、方案概述及功能-看功能（WIFI漫游管理）"}]}], "index": 0, "level": 1}, {"bbox": [197, 113, 311, 136], "type": "title", "lines": [{"bbox": [197, 113, 311, 136], "spans": [{"bbox": [197, 113, 311, 136], "type": "text", "content": "统一云端配置"}]}], "index": 1, "level": 1}, {"bbox": [59, 154, 443, 204], "type": "text", "lines": [{"bbox": [59, 154, 443, 204], "spans": [{"bbox": [59, 154, 443, 204], "type": "text", "content": "云端统一对路由器、融合网关进行漫游WiFi网络统一配置"}]}], "index": 2}, {"type": "image", "bbox": [76, 229, 432, 462], "blocks": [{"bbox": [76, 229, 432, 462], "lines": [{"bbox": [76, 229, 432, 462], "spans": [{"bbox": [76, 229, 432, 462], "type": "image", "image_path": "c246eb54b6b154d999e24f9d4ce7e3f14b56591ddbf4a3076e01c2b1acc1da96.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [634, 113, 766, 137], "type": "title", "lines": [{"bbox": [634, 113, 766, 137], "spans": [{"bbox": [634, 113, 766, 137], "type": "text", "content": "登录portal定制"}]}], "index": 4, "level": 1}, {"bbox": [506, 154, 887, 204], "type": "text", "lines": [{"bbox": [506, 154, 887, 204], "spans": [{"bbox": [506, 154, 887, 204], "type": "text", "content": "对用户登录portal根据不同模板更换宣传图、宣传语等内容"}]}], "index": 5}, {"type": "image", "bbox": [518, 227, 890, 463], "blocks": [{"bbox": [518, 227, 890, 463], "lines": [{"bbox": [518, 227, 890, 463], "spans": [{"bbox": [518, 227, 890, 463], "type": "image", "image_path": "4740a934226a0d953f8a63f11b3a287db640256643489889bd83e0c29969499b.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 14}, {"para_blocks": [{"bbox": [64, 21, 549, 51], "type": "title", "lines": [{"bbox": [64, 21, 549, 51], "spans": [{"bbox": [64, 21, 549, 51], "type": "text", "content": "2、方案概述及功能-看功能（WIFI漫游管理）"}]}], "index": 0, "level": 1}, {"bbox": [198, 116, 311, 139], "type": "title", "lines": [{"bbox": [198, 116, 311, 139], "spans": [{"bbox": [198, 116, 311, 139], "type": "text", "content": "同酒店内漫游"}]}], "index": 1, "level": 1}, {"bbox": [59, 156, 442, 205], "type": "text", "lines": [{"bbox": [59, 156, 442, 205], "spans": [{"bbox": [59, 156, 442, 205], "type": "text", "content": "- 酒店客房内、公区（走廊、大厅、餐厅等）全WiFi覆盖，用户登录一次，WiFi一直连接"}]}], "index": 2}, {"type": "image", "bbox": [74, 214, 417, 474], "blocks": [{"bbox": [74, 214, 417, 474], "lines": [{"bbox": [74, 214, 417, 474], "spans": [{"bbox": [74, 214, 417, 474], "type": "image", "image_path": "04683374b30106df967fa0762e4a4bca0df77c30ff6f97841bd5b7fa6f35d722.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [580, 116, 821, 139], "type": "title", "lines": [{"bbox": [580, 116, 821, 139], "spans": [{"bbox": [580, 116, 821, 139], "type": "text", "content": "连锁酒店，跨酒店免认证登录"}]}], "index": 4, "level": 1}, {"bbox": [506, 156, 865, 177], "type": "text", "lines": [{"bbox": [506, 156, 865, 177], "spans": [{"bbox": [506, 156, 865, 177], "type": "text", "content": "- 连锁酒店，酒店A认证过，酒店B无需再次认证。"}]}], "index": 5}, {"type": "image", "bbox": [516, 223, 892, 474], "blocks": [{"bbox": [516, 223, 892, 474], "lines": [{"bbox": [516, 223, 892, 474], "spans": [{"bbox": [516, 223, 892, 474], "type": "image", "image_path": "3e54e3f32708ffbf7cc6394def403366f5fa2a3f60a69c81f4584c1452af8052.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 15}, {"para_blocks": [{"bbox": [64, 21, 549, 51], "type": "title", "lines": [{"bbox": [64, 21, 549, 51], "spans": [{"bbox": [64, 21, 549, 51], "type": "text", "content": "2、方案概述及功能-看功能（WIFI漫游管理）"}]}], "index": 0, "level": 1}, {"bbox": [187, 107, 300, 130], "type": "title", "lines": [{"bbox": [187, 107, 300, 130], "spans": [{"bbox": [187, 107, 300, 130], "type": "text", "content": "扫码电视投屏"}]}], "index": 1, "level": 1}, {"bbox": [50, 147, 432, 198], "type": "text", "lines": [{"bbox": [50, 147, 432, 198], "spans": [{"bbox": [50, 147, 432, 198], "type": "text", "content": "扫描电视上的投屏二维码连接投屏WiFi，将手机上的视频投到酒店电视。"}]}], "index": 2}, {"type": "image", "bbox": [64, 270, 406, 442], "blocks": [{"bbox": [64, 270, 406, 442], "lines": [{"bbox": [64, 270, 406, 442], "spans": [{"bbox": [64, 270, 406, 442], "type": "image", "image_path": "63d4dc1de9214b20be2cc59dc449d6e35cf166a2762d15a938d55023f574a9bb.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [631, 107, 751, 130], "type": "title", "lines": [{"bbox": [631, 107, 751, 130], "spans": [{"bbox": [631, 107, 751, 130], "type": "text", "content": "iot免认证登录"}]}], "index": 4, "level": 1}, {"bbox": [496, 147, 883, 198], "type": "text", "lines": [{"bbox": [496, 147, 883, 198], "spans": [{"bbox": [496, 147, 883, 198], "type": "text", "content": "- lot设备、酒店送餐机器人、监控、客控等设备，免认证随时随地上网。"}]}], "index": 5}, {"type": "image", "bbox": [528, 231, 869, 458], "blocks": [{"bbox": [528, 231, 869, 458], "lines": [{"bbox": [528, 231, 869, 458], "spans": [{"bbox": [528, 231, 869, 458], "type": "image", "image_path": "345cdf5557936f31e5cfb085991343b7cf8a733db35a2560d81e0bcfce2fcb97.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 16}, {"para_blocks": [{"bbox": [64, 22, 549, 51], "type": "title", "lines": [{"bbox": [64, 22, 549, 51], "spans": [{"bbox": [64, 22, 549, 51], "type": "text", "content": "2、方案概述及功能-看功能（WIFI漫游管理）"}]}], "index": 0, "level": 1}, {"bbox": [434, 122, 511, 145], "type": "title", "lines": [{"bbox": [434, 122, 511, 145], "spans": [{"bbox": [434, 122, 511, 145], "type": "text", "content": "告警管理"}]}], "index": 1, "level": 1}, {"bbox": [62, 162, 474, 184], "type": "text", "lines": [{"bbox": [62, 162, 474, 184], "spans": [{"bbox": [62, 162, 474, 184], "type": "text", "content": "- 对设备异常进行告警，包括离线告警、连接异常告警等"}]}], "index": 2}, {"type": "image", "bbox": [95, 224, 869, 446], "blocks": [{"bbox": [95, 224, 869, 446], "lines": [{"bbox": [95, 224, 869, 446], "spans": [{"bbox": [95, 224, 869, 446], "type": "image", "image_path": "1669a015009d94c379e772dcac1200835716caa1f2816d69bd16dbd5b7857855.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 17}, {"para_blocks": [{"bbox": [64, 22, 203, 51], "type": "title", "lines": [{"bbox": [64, 22, 203, 51], "spans": [{"bbox": [64, 22, 203, 51], "type": "text", "content": "4、支撑方案"}]}], "index": 0, "level": 1}, {"bbox": [24, 75, 936, 127], "type": "text", "lines": [{"bbox": [24, 75, 936, 127], "spans": [{"bbox": [24, 75, 936, 127], "type": "text", "content": "- 轻量化智慧酒店解决方案由物联网公司提供一体化支撑服务，物联网公司拥有多个驻点分支机构，多种厂商资源整合能力，以及丰富的智慧酒店项目管理经验，达到覆盖全国智慧酒店项目的售前、交付和售后能力。"}]}], "index": 1}, {"type": "image", "bbox": [45, 150, 120, 210], "blocks": [{"bbox": [45, 150, 120, 210], "lines": [{"bbox": [45, 150, 120, 210], "spans": [{"bbox": [45, 150, 120, 210], "type": "image", "image_path": "78afa08e30a2f7adda85ea54561c4fe7afa8b3210b0c9ac326c492615a10dbca.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}, {"bbox": [129, 166, 210, 190], "type": "title", "lines": [{"bbox": [129, 166, 210, 190], "spans": [{"bbox": [129, 166, 210, 190], "type": "text", "content": "交付流程"}]}], "index": 3, "level": 1}, {"bbox": [144, 207, 223, 259], "type": "text", "lines": [{"bbox": [144, 207, 223, 259], "spans": [{"bbox": [144, 207, 223, 259], "type": "text", "content": "了解商机详情现场勘查建议整理勘察需求"}]}], "index": 4}, {"bbox": [330, 207, 409, 259], "type": "text", "lines": [{"bbox": [330, 207, 409, 259], "spans": [{"bbox": [330, 207, 409, 259], "type": "text", "content": "酒店基础信息酒店房型勘察房间现场照片"}]}], "index": 5}, {"bbox": [515, 207, 593, 259], "type": "text", "lines": [{"bbox": [515, 207, 593, 259], "spans": [{"bbox": [515, 207, 593, 259], "type": "text", "content": "合同、借货函确定解决方案确定实施方案"}]}], "index": 6}, {"bbox": [714, 210, 832, 264], "type": "text", "lines": [{"bbox": [714, 210, 832, 264], "spans": [{"bbox": [714, 210, 832, 264], "type": "text", "content": "项目监督项目施工 客户培训系统调试 项目验收"}]}], "index": 7}, {"bbox": [153, 289, 214, 307], "type": "title", "lines": [{"bbox": [153, 289, 214, 307], "spans": [{"bbox": [153, 289, 214, 307], "type": "text", "content": "商机交底"}]}], "index": 8, "level": 1}, {"bbox": [338, 289, 400, 307], "type": "title", "lines": [{"bbox": [338, 289, 400, 307], "spans": [{"bbox": [338, 289, 400, 307], "type": "text", "content": "现场勘查"}]}], "index": 9, "level": 1}, {"bbox": [525, 289, 583, 307], "type": "title", "lines": [{"bbox": [525, 289, 583, 307], "spans": [{"bbox": [525, 289, 583, 307], "type": "text", "content": "实施交底"}]}], "index": 10, "level": 1}, {"bbox": [741, 289, 802, 307], "type": "title", "lines": [{"bbox": [741, 289, 802, 307], "spans": [{"bbox": [741, 289, 802, 307], "type": "text", "content": "进场实施"}]}], "index": 11, "level": 1}, {"bbox": [134, 324, 214, 348], "type": "title", "lines": [{"bbox": [134, 324, 214, 348], "spans": [{"bbox": [134, 324, 214, 348], "type": "text", "content": "支撑能力"}]}], "index": 12, "level": 1}, {"bbox": [100, 372, 160, 389], "type": "title", "lines": [{"bbox": [100, 372, 160, 389], "spans": [{"bbox": [100, 372, 160, 389], "type": "text", "content": "产品支撑"}]}], "index": 13, "level": 1}, {"bbox": [274, 371, 333, 389], "type": "title", "lines": [{"bbox": [274, 371, 333, 389], "spans": [{"bbox": [274, 371, 333, 389], "type": "text", "content": "平台支撑"}]}], "index": 14, "level": 1}, {"bbox": [446, 371, 504, 389], "type": "title", "lines": [{"bbox": [446, 371, 504, 389], "spans": [{"bbox": [446, 371, 504, 389], "type": "text", "content": "装维支撑"}]}], "index": 15, "level": 1}, {"bbox": [637, 371, 696, 389], "type": "title", "lines": [{"bbox": [637, 371, 696, 389], "spans": [{"bbox": [637, 371, 696, 389], "type": "text", "content": "运营支撑"}]}], "index": 16, "level": 1}, {"bbox": [825, 371, 886, 389], "type": "title", "lines": [{"bbox": [825, 371, 886, 389], "spans": [{"bbox": [825, 371, 886, 389], "type": "text", "content": "需求支撑"}]}], "index": 17, "level": 1}, {"bbox": [48, 400, 210, 496], "type": "text", "lines": [{"bbox": [48, 400, 210, 496], "spans": [{"bbox": [48, 400, 210, 496], "type": "text", "content": "标准化产品由物联网公司负责产品模型制定、平台及应用适配规范标准制定、产品套餐上架等工作，确保产品性能符合智慧酒店的产品需求，并形成产品清单按需更新并下发。"}]}], "index": 18}, {"bbox": [227, 400, 374, 495], "type": "text", "lines": [{"bbox": [227, 400, 374, 495], "spans": [{"bbox": [227, 400, 374, 495], "type": "text", "content": "利用平台维护诊断工具提供"}, {"bbox": [227, 400, 374, 495], "type": "inline_equation", "content": "7\\times 24"}, {"bbox": [227, 400, 374, 495], "type": "text", "content": " 小时运营保障支撑。日常保障期间，各个保障组负责平台监控、故障处理以及协调工作，以保障平台业务安全稳定运行。"}]}], "index": 19}, {"bbox": [400, 400, 542, 495], "type": "text", "lines": [{"bbox": [400, 400, 542, 495], "spans": [{"bbox": [400, 400, 542, 495], "type": "text", "content": "物联网公司提供装维培训课件和技术支持。对于不具备本地网统筹省市，物联网公司区域分支机构负责协调中移铁通或合作伙伴做好对接，提供装维交付服务。"}]}], "index": 20}, {"bbox": [594, 399, 743, 493], "type": "text", "lines": [{"bbox": [594, 399, 743, 493], "spans": [{"bbox": [594, 399, 743, 493], "type": "text", "content": "物联网公司做好日常运维工作，协同省公司加强对一线业务人员的全方位培训，协助推动支撑各省份智慧酒店落地推广，同时定期牵头开展用户调研，根据用户需求不断完善产品功能、提升产品质量。"}]}], "index": 21}, {"bbox": [788, 399, 917, 480], "type": "text", "lines": [{"bbox": [788, 399, 917, 480], "spans": [{"bbox": [788, 399, 917, 480], "type": "text", "content": "物联网公司具备七大分支机构，需求支撑能力覆盖全国范围，配合推动项目签订、商务交流、商机转化等，保障客户需求得到及时响应与商机流转。"}]}], "index": 22}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 18}, {"para_blocks": [{"bbox": [64, 21, 398, 51], "type": "title", "lines": [{"bbox": [64, 21, 398, 51], "spans": [{"bbox": [64, 21, 398, 51], "type": "text", "content": "5、落地案例一：山西滨河饭店"}]}], "index": 0, "level": 1}, {"bbox": [40, 79, 930, 130], "type": "text", "lines": [{"bbox": [40, 79, 930, 130], "spans": [{"bbox": [40, 79, 930, 130], "type": "text", "content": "项目概况：山西滨河酒店是山西文旅集团旗下酒店，有173间客房，酒店为提高客户入住体验，从线上线下提升酒店口碑，计划开展智能化改造。"}]}], "index": 1}, {"bbox": [136, 147, 204, 167], "type": "title", "lines": [{"bbox": [136, 147, 204, 167], "spans": [{"bbox": [136, 147, 204, 167], "type": "text", "content": "主要诉求"}]}], "index": 2, "level": 1}, {"bbox": [454, 147, 523, 167], "type": "title", "lines": [{"bbox": [454, 147, 523, 167], "spans": [{"bbox": [454, 147, 523, 167], "type": "text", "content": "业务概述"}]}], "index": 3, "level": 1}, {"bbox": [362, 176, 883, 195], "type": "text", "lines": [{"bbox": [362, 176, 883, 195], "spans": [{"bbox": [362, 176, 883, 195], "type": "text", "content": "项目总额近200万，物联网公司与客户直签与移动双算，移动通过ICT- IoT科目计收。"}]}], "index": 4}, {"type": "image", "bbox": [66, 180, 285, 349], "blocks": [{"bbox": [66, 180, 285, 349], "lines": [{"bbox": [66, 180, 285, 349], "spans": [{"bbox": [66, 180, 285, 349], "type": "image", "image_path": "904903f4cfe081fcdd0aacdcd10cb26582183c86becb81844df073583e41fe50.jpg"}]}], "index": 5, "type": "image_body"}], "index": 5}, {"bbox": [455, 214, 523, 235], "type": "title", "lines": [{"bbox": [455, 214, 523, 235], "spans": [{"bbox": [455, 214, 523, 235], "type": "text", "content": "核心应用"}]}], "index": 6, "level": 1}, {"bbox": [42, 355, 315, 501], "type": "text", "lines": [{"bbox": [42, 355, 315, 501], "spans": [{"bbox": [42, 355, 315, 501], "type": "text", "content": "本项目拟开展133间客房完成智慧酒店建设，通过打造酒店智能云平台，依托智能终端、智能语音、自助机、机器人等现代接入方式，将培训基地的传统酒店模式升级为具备智能大堂、智能客房、智能园区并提供全新交互能力的智慧酒店。"}]}], "index": 7}, {"type": "image", "bbox": [350, 248, 633, 417], "blocks": [{"bbox": [350, 248, 633, 417], "lines": [{"bbox": [350, 248, 633, 417], "spans": [{"bbox": [350, 248, 633, 417], "type": "image", "image_path": "f6b8eea9b672afa5d593453f604677ce0c5a3f6e7c31a1b664d6c32f49133a4c.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}, {"bbox": [647, 255, 920, 425], "type": "text", "lines": [{"bbox": [647, 255, 920, 425], "spans": [{"bbox": [647, 255, 920, 425], "type": "text", "content": "协同智慧园区团队，建设内容包含智慧园区平台，以及包含了引领送物机器人、无人自助入住机、智能开关、智能语音、智能窗帘等系统。引入智慧酒店信息化解决方案，打造行业应用标杆，实现“处处无人又随时有人、能自助尽量自助”的服务体验，提高酒店智能化水平。"}]}], "index": 9}, {"type": "image", "bbox": [345, 436, 878, 501], "blocks": [{"bbox": [345, 436, 878, 501], "lines": [{"bbox": [345, 436, 878, 501], "spans": [{"bbox": [345, 436, 878, 501], "type": "image", "image_path": "a5ce66dc69bfaf303dd9d6d87f929e2bc77db5255509d9235dbb17266918cdb0.jpg"}]}], "index": 10, "type": "image_body"}], "index": 10}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 19}, {"para_blocks": [{"bbox": [64, 20, 612, 51], "type": "title", "lines": [{"bbox": [64, 20, 612, 51], "spans": [{"bbox": [64, 20, 612, 51], "type": "text", "content": "5、落地案例二：云南锦洲大酒店酒管系统建设项目"}]}], "index": 0, "level": 1}, {"bbox": [40, 78, 935, 131], "type": "text", "lines": [{"bbox": [40, 78, 935, 131], "spans": [{"bbox": [40, 78, 935, 131], "type": "text", "content": "项目概况：锦洲大酒店为一家新建酒店，整个酒店一共83间房间，期望通过部署全套酒店管理系统实现对酒店住前、住中、住后多个场景的统一管理，对酒店财务报表的一键查询，实现酒店的数字化、规范化运营。"}]}], "index": 1}, {"bbox": [136, 147, 206, 168], "type": "title", "lines": [{"bbox": [136, 147, 206, 168], "spans": [{"bbox": [136, 147, 206, 168], "type": "text", "content": "实现效果"}]}], "index": 2, "level": 1}, {"bbox": [42, 184, 314, 439], "type": "text", "lines": [{"bbox": [42, 184, 314, 439], "spans": [{"bbox": [42, 184, 314, 439], "type": "text", "content": "在提供宽带、和商务TV基础业务的基础上，采用一键标准化部署，为酒店管理方前台系统(PMS)、移动端系统、接口系统等功能模块，实现快速落地，为酒店提高了高效线上管理系统，提高前台效率，降低人工成本，让酒店老板使用小程序移动端随时掌握经营状况。更好地提高酒店方的运营能力和服务品质。"}]}], "index": 3}, {"type": "image", "bbox": [362, 189, 900, 505], "blocks": [{"bbox": [362, 189, 900, 505], "lines": [{"bbox": [362, 189, 900, 505], "spans": [{"bbox": [362, 189, 900, 505], "type": "image", "image_path": "95aa1a7d48c158a85daf463571f18ed6590278647786acb974fb5e5ba6d0c0c5.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 20}], "_backend": "vlm", "_version_name": "2.1.9"}