{"pdf_info": [{"para_blocks": [{"bbox": [50, 169, 911, 284], "type": "title", "lines": [{"bbox": [50, 169, 911, 284], "spans": [{"bbox": [50, 169, 911, 284], "type": "text", "content": "河南警察学院5G+VR警务实战训练系统解决方案"}]}], "index": 0, "level": 1}, {"bbox": [407, 315, 554, 400], "type": "text", "lines": [{"bbox": [407, 315, 554, 400], "spans": [{"bbox": [407, 315, 554, 400], "type": "text", "content": "河南移动  2024年7月"}]}], "index": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 0}, {"para_blocks": [{"bbox": [330, 163, 350, 186], "type": "text", "lines": [{"bbox": [330, 163, 350, 186], "spans": [{"bbox": [330, 163, 350, 186], "type": "text", "content": "1"}]}], "index": 0}, {"bbox": [474, 162, 669, 191], "type": "title", "lines": [{"bbox": [474, 162, 669, 191], "spans": [{"bbox": [474, 162, 669, 191], "type": "text", "content": "市场前景及需求分析"}]}], "index": 1, "level": 1}, {"bbox": [148, 253, 217, 289], "type": "title", "lines": [{"bbox": [148, 253, 217, 289], "spans": [{"bbox": [148, 253, 217, 289], "type": "text", "content": "目录"}]}], "index": 2, "level": 1}, {"bbox": [332, 285, 351, 306], "type": "text", "lines": [{"bbox": [332, 285, 351, 306], "spans": [{"bbox": [332, 285, 351, 306], "type": "text", "content": "2"}]}], "index": 3}, {"bbox": [528, 284, 616, 310], "type": "text", "lines": [{"bbox": [528, 284, 616, 310], "spans": [{"bbox": [528, 284, 616, 310], "type": "text", "content": "解决方案"}]}], "index": 4}, {"bbox": [156, 334, 205, 347], "type": "text", "lines": [{"bbox": [156, 334, 205, 347], "spans": [{"bbox": [156, 334, 205, 347], "type": "text", "content": "CONTENT"}]}], "index": 5}, {"bbox": [332, 404, 350, 423], "type": "text", "lines": [{"bbox": [332, 404, 350, 423], "spans": [{"bbox": [332, 404, 350, 423], "type": "text", "content": "3"}]}], "index": 6}, {"bbox": [528, 403, 616, 430], "type": "text", "lines": [{"bbox": [528, 403, 616, 430], "spans": [{"bbox": [528, 403, 616, 430], "type": "text", "content": "案例介绍"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 1}, {"para_blocks": [{"bbox": [33, 45, 133, 74], "type": "title", "lines": [{"bbox": [33, 45, 133, 74], "spans": [{"bbox": [33, 45, 133, 74], "type": "text", "content": "市场前景"}]}], "index": 0, "level": 1}, {"bbox": [18, 93, 936, 135], "type": "text", "lines": [{"bbox": [18, 93, 936, 135], "spans": [{"bbox": [18, 93, 936, 135], "type": "text", "content": "- 方案介绍：5G+VR警务实训解决方案主要面向全国范围内公安类院校，提供5G+VR虚拟警务实训能力，解决线下警务实训费用高和时效性差等问题。以河南为例，警察学院运用该项方案，每年可为省公安厅节省培训经费6000余万元，每月增加在岗警力3000余人。项目目前已具备交付能力。"}]}], "index": 1}, {"bbox": [461, 147, 522, 165], "type": "title", "lines": [{"bbox": [461, 147, 522, 165], "spans": [{"bbox": [461, 147, 522, 165], "type": "text", "content": "市场前景"}]}], "index": 2, "level": 1}, {"bbox": [40, 177, 935, 217], "type": "text", "lines": [{"bbox": [40, 177, 935, 217], "spans": [{"bbox": [40, 177, 935, 217], "type": "text", "content": "公安部党委发布《关于开展第四轮全国公安机关素质强警交流合作的实施意见》，明确自2022年至2025年，组织开展全国第四轮素质强警交流合作。其中虚拟仿真专题由河南警察学院牵头实施，首批涉及河南、广东、贵州等5省6所警察学院。"}]}], "index": 3}, {"bbox": [40, 221, 933, 259], "type": "text", "lines": [{"bbox": [40, 221, 933, 259], "spans": [{"bbox": [40, 221, 933, 259], "type": "text", "content": "按照要求，需在涉及警院所在省内部署虚拟实训系统，用于警察系统日常实战演练工作。河南省警察学院作为牵头单位，目前已经联合河南移动展开了虚拟实训系统的部署工作，省内项目金额1.5亿元，同时广东、贵州、福建等省份摸排商机共计3210万元，市场前景广阔。"}]}], "index": 4}, {"type": "table", "bbox": [36, 270, 945, 537], "blocks": [{"bbox": [36, 270, 945, 537], "lines": [{"bbox": [36, 270, 945, 537], "spans": [{"bbox": [36, 270, 945, 537], "type": "table", "html": "<table><tr><td rowspan=\"18\" colspan=\"2\">全国公安司法类院校分布情况</td><td>省份</td><td>学校名称</td><td>学段</td><td>省份</td><td>学校名称</td><td>学段</td><td>省份</td><td>学校名称</td><td>学段</td><td>省份</td><td>学校名称</td><td>学段</td></tr><tr><td>公安部北京</td><td>中国人民公安大学</td><td>本科</td><td>北京</td><td>北京警察学院</td><td>本科</td><td rowspan=\"2\">辽宁</td><td>辽宁警察学院</td><td>本科</td><td rowspan=\"2\">湖南</td><td>湖南警察学院</td><td>本科</td></tr><tr><td>公安部河北</td><td>中国人民警察大学</td><td>本科</td><td rowspan=\"16\">河南</td><td>河南警察学院</td><td>本科</td><td>辽宁公安司法管理干部学院</td><td>成人高校</td><td>湖南司法警官职业学院</td><td>专科</td></tr><tr><td>公安部辽宁</td><td>中国刑事警察学院</td><td>本科</td><td>河南司法警官职业学院</td><td>专科</td><td rowspan=\"3\">山西</td><td>山西警察学院</td><td>本科</td><td rowspan=\"2\">四川</td><td>四川警察学院</td><td>本科</td></tr><tr><td>公安部北京</td><td>公安部管理干部学院</td><td>成人高校</td><td>中央司法警官学院</td><td>本科</td><td>山西省司法学校</td><td>中职</td><td>四川司法警官职业学院</td><td>专科</td></tr><tr><td>公安部江苏</td><td>南京警察学院</td><td>本科</td><td>河北公安警察职业学院</td><td>专科</td><td>山西省临汾人民警察学校</td><td>中职</td><td rowspan=\"2\">云南</td><td>云南司法警官职业学院</td><td>专科</td></tr><tr><td>公安部河南</td><td>郑州警察学院</td><td>本科</td><td>河北司法警官职业学院</td><td>专科</td><td rowspan=\"3\">新疆</td><td>新疆警察学院</td><td>本科</td><td>公安消防部队高等科学学校</td><td>专科</td></tr><tr><td></td><td></td><td></td><td>张家口市人民武装学校</td><td>中职</td><td>新疆司法警官职业学院</td><td>专科</td><td>江苏</td><td>江苏省司法警官高等职业学校</td><td>中职</td></tr><tr><td></td><td></td><td></td><td>衡水北方司法学校</td><td>中职</td><td>新疆司法警官学校</td><td>中职</td><td>天津</td><td>天津公安警官职业学院</td><td>专科</td></tr><tr><td></td><td></td><td></td><td>广东司法警官职业学院</td><td>专科</td><td rowspan=\"6\">黑龙江</td><td>黑龙江司法警官职业学院</td><td>专科</td><td>内蒙</td><td>内蒙古警察职业学院</td><td>专科</td></tr><tr><td></td><td></td><td></td><td>广州市公安管理干部学院</td><td>成人高校</td><td>黑龙江公安警官职业学院</td><td>专科</td><td>上海</td><td>上海公安学院</td><td>本科</td></tr><tr><td></td><td></td><td></td><td>广东省公安司法警官干部学院</td><td>成人高校</td><td>湖北武汉公安警官干部学院</td><td>成人高校</td><td>浙江</td><td>浙江警察学院</td><td>本科</td></tr><tr><td></td><td></td><td></td><td>广州市司法职业学校</td><td>中职</td><td>公安县职业技术教育中心学校</td><td>中职</td><td>安徽</td><td>安徽公安职业学院</td><td>专科</td></tr><tr><td></td><td></td><td></td><td>化州市遂江司法职业学校</td><td>中职</td><td>吉林警察学院</td><td>本科</td><td>福建</td><td>福建警察学院</td><td>本科</td></tr><tr><td></td><td></td><td></td><td>江西警察学院</td><td>本科</td><td>吉林司法警官职业学院</td><td>专科</td><td>广西</td><td>广西警察学院</td><td>本科</td></tr><tr><td></td><td></td><td></td><td>江西司法警官职业学院</td><td>专科</td><td rowspan=\"2\">山东</td><td>山东警察学院</td><td>本科</td><td>重庆</td><td>重庆警察学院</td><td>本科</td></tr><tr><td></td><td></td><td></td><td>新余司法警官学校</td><td>中职</td><td>山东司法警官职业学院</td><td>专科</td><td>贵州</td><td>贵州警察学院</td><td>本科</td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>甘肃</td><td>甘肃警察职业学院</td><td>专科</td><td></td></tr></table>", "image_path": "6105538c0a6d431c7ebbfa1fb783908ddaf28a01310df78cd9f2a02759377ddb.jpg"}]}], "index": 5, "type": "table_body"}], "index": 5}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 2}, {"para_blocks": [{"bbox": [31, 25, 325, 55], "type": "title", "lines": [{"bbox": [31, 25, 325, 55], "spans": [{"bbox": [31, 25, 325, 55], "type": "text", "content": "自有能力、实验室能力共享"}]}], "index": 0, "level": 1}, {"bbox": [18, 94, 941, 140], "type": "text", "lines": [{"bbox": [18, 94, 941, 140], "spans": [{"bbox": [18, 94, 941, 140], "type": "text", "content": "河南公司与河南省警察学院成立联创实验室，实验室国家级、省级优秀课程及相关软著进行共享，共同推进项目在全国范围内的复制推广，同时针对5G+XR类场景，河南移动基于5G的XR场景示范应用平台软件研发进行立项，全面助力项目优化与拓展。"}]}], "index": 1}, {"type": "image", "bbox": [18, 147, 938, 496], "blocks": [{"bbox": [18, 147, 938, 496], "lines": [{"bbox": [18, 147, 938, 496], "spans": [{"bbox": [18, 147, 938, 496], "type": "image", "image_path": "ea76138a515548d08e6cd692761414bf68e8a5877365ce0c5638e5be5d881ff4.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 3}, {"para_blocks": [{"bbox": [31, 45, 133, 74], "type": "title", "lines": [{"bbox": [31, 45, 133, 74], "spans": [{"bbox": [31, 45, 133, 74], "type": "text", "content": "需求分析"}]}], "index": 0, "level": 1}, {"type": "image", "bbox": [66, 79, 885, 537], "blocks": [{"bbox": [66, 79, 885, 537], "lines": [{"bbox": [66, 79, 885, 537], "spans": [{"bbox": [66, 79, 885, 537], "type": "image", "image_path": "e1efdc70bb9171384e2ddc3d25c659e8b82a4904d9a1e771424c02baf8227280.jpg"}]}], "index": 1, "type": "image_body"}], "index": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 4}, {"para_blocks": [{"bbox": [149, 253, 217, 289], "type": "title", "lines": [{"bbox": [149, 253, 217, 289], "spans": [{"bbox": [149, 253, 217, 289], "type": "text", "content": "目录"}]}], "index": 0, "level": 1}, {"bbox": [330, 165, 348, 185], "type": "text", "lines": [{"bbox": [330, 165, 348, 185], "spans": [{"bbox": [330, 165, 348, 185], "type": "text", "content": "1"}]}], "index": 1}, {"bbox": [474, 162, 669, 190], "type": "title", "lines": [{"bbox": [474, 162, 669, 190], "spans": [{"bbox": [474, 162, 669, 190], "type": "text", "content": "市场前景及需求分析"}]}], "index": 2, "level": 1}, {"bbox": [332, 285, 351, 306], "type": "text", "lines": [{"bbox": [332, 285, 351, 306], "spans": [{"bbox": [332, 285, 351, 306], "type": "text", "content": "2"}]}], "index": 3}, {"bbox": [527, 284, 616, 310], "type": "title", "lines": [{"bbox": [527, 284, 616, 310], "spans": [{"bbox": [527, 284, 616, 310], "type": "text", "content": "解决方案"}]}], "index": 4, "level": 1}, {"bbox": [155, 334, 206, 347], "type": "text", "lines": [{"bbox": [155, 334, 206, 347], "spans": [{"bbox": [155, 334, 206, 347], "type": "text", "content": "CONTENT"}]}], "index": 5}, {"bbox": [332, 404, 350, 423], "type": "text", "lines": [{"bbox": [332, 404, 350, 423], "spans": [{"bbox": [332, 404, 350, 423], "type": "text", "content": "3"}]}], "index": 6}, {"bbox": [527, 403, 616, 430], "type": "title", "lines": [{"bbox": [527, 403, 616, 430], "spans": [{"bbox": [527, 403, 616, 430], "type": "text", "content": "案例介绍"}]}], "index": 7, "level": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 5}, {"para_blocks": [{"bbox": [9, 17, 240, 46], "type": "title", "lines": [{"bbox": [9, 17, 240, 46], "spans": [{"bbox": [9, 17, 240, 46], "type": "text", "content": "省域5G专网实训专网"}]}], "index": 0, "level": 1}, {"bbox": [23, 71, 941, 169], "type": "text", "lines": [{"bbox": [23, 71, 941, 169], "spans": [{"bbox": [23, 71, 941, 169], "type": "text", "content": "项目打造基于全省可互联互通的5G切片网络，学校作为中心节点采用4.9G的5G专网，建设5G室分4个（4个BBU挂载8个PRRU），并使本地化部署的独享UPF分流带宽。项目同时在地市节点部署公网2.6G,30%切片资源预留即可满足要求，同时依托物联网公司OneCyber底座，对5G专网整体网络质量进行实时监控。项目在地市节点部署云渲染服务器以达到降低下行带宽的需求。通过引入空口智能预调度功能，可缩减时延4- 5ms。在基站侧开发了特色功能，如智能识别VR业务，分配专有5QI进行优先调度；在5G传输方面，项目通过Qos等技术可满足5G传输侧时延达到10ms。"}]}], "index": 1}, {"type": "image", "bbox": [199, 184, 849, 463], "blocks": [{"bbox": [199, 184, 849, 463], "lines": [{"bbox": [199, 184, 849, 463], "spans": [{"bbox": [199, 184, 849, 463], "type": "image", "image_path": "18542efba122e711b3a596281cb87022c387f3136114223c5dd5b06228279d77.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}, {"bbox": [221, 479, 686, 508], "type": "text", "lines": [{"bbox": [221, 479, 686, 508], "spans": [{"bbox": [221, 479, 686, 508], "type": "text", "content": "大范围互联、移动性强、低延时、高安全性"}]}], "index": 3}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 6}, {"para_blocks": [{"bbox": [10, 17, 181, 46], "type": "title", "lines": [{"bbox": [10, 17, 181, 46], "spans": [{"bbox": [10, 17, 181, 46], "type": "text", "content": "警务实训云平台"}]}], "index": 0, "level": 1}, {"bbox": [23, 71, 943, 144], "type": "text", "lines": [{"bbox": [23, 71, 943, 144], "spans": [{"bbox": [23, 71, 943, 144], "type": "text", "content": "警务实训云平台采用边缘智能小站EIS方案，将云基础设施部署在用户现场，具备数据不出场、超低时延、资源独占等特性的边缘软硬一体化交付产品，满足用户低成本运营、资源专享、数据不出场等需求，提供虚拟机、容器、存储、网络、镜像等多种云服务及资源、运维监控等管理能力，可广泛应用于政企单位分支机构/智慧工厂/智慧园区业务系统等局域边缘场景。"}]}], "index": 1}, {"bbox": [122, 181, 239, 202], "type": "title", "lines": [{"bbox": [122, 181, 239, 202], "spans": [{"bbox": [122, 181, 239, 202], "type": "text", "content": "云平台EIS特点"}]}], "index": 2, "level": 1}, {"bbox": [47, 227, 147, 246], "type": "title", "lines": [{"bbox": [47, 227, 147, 246], "spans": [{"bbox": [47, 227, 147, 246], "type": "text", "content": "轻量化，易部署"}]}], "index": 3, "level": 1}, {"bbox": [48, 252, 146, 271], "type": "text", "lines": [{"bbox": [48, 252, 146, 271], "spans": [{"bbox": [48, 252, 146, 271], "type": "text", "content": "3节点起步交付"}]}], "index": 4}, {"bbox": [47, 278, 105, 295], "type": "title", "lines": [{"bbox": [47, 278, 105, 295], "spans": [{"bbox": [47, 278, 105, 295], "type": "text", "content": "能力丰富"}]}], "index": 5, "level": 1}, {"bbox": [48, 303, 310, 344], "type": "text", "lines": [{"bbox": [48, 303, 310, 344], "spans": [{"bbox": [48, 303, 310, 344], "type": "text", "content": "提供云主机、容器、镜像、云硬盘、VPC、网关、EIP、对象存储等 "}, {"bbox": [48, 303, 310, 344], "type": "inline_equation", "content": "14+"}, {"bbox": [48, 303, 310, 344], "type": "text", "content": " 种能力"}]}], "index": 6}, {"bbox": [48, 348, 196, 366], "type": "title", "lines": [{"bbox": [48, 348, 196, 366], "spans": [{"bbox": [48, 348, 196, 366], "type": "text", "content": "本地管理/云端统一运维"}]}], "index": 7, "level": 1}, {"bbox": [49, 374, 251, 392], "type": "text", "lines": [{"bbox": [49, 374, 251, 392], "spans": [{"bbox": [49, 374, 251, 392], "type": "text", "content": "本地云管平台供用户自服务管理"}]}], "index": 8}, {"bbox": [50, 398, 180, 415], "type": "text", "lines": [{"bbox": [50, 398, 180, 415], "spans": [{"bbox": [50, 398, 180, 415], "type": "text", "content": "云端集中化统一监控"}]}], "index": 9}, {"bbox": [48, 424, 105, 441], "type": "title", "lines": [{"bbox": [48, 424, 105, 441], "spans": [{"bbox": [48, 424, 105, 441], "type": "text", "content": "产品规格"}]}], "index": 10, "level": 1}, {"bbox": [49, 448, 314, 490], "type": "text", "lines": [{"bbox": [49, 448, 314, 490], "spans": [{"bbox": [49, 448, 314, 490], "type": "text", "content": "融合型、GPU型、国产化型、X86、ARM、对象存储"}]}], "index": 11}, {"bbox": [575, 170, 683, 190], "type": "title", "lines": [{"bbox": [575, 170, 683, 190], "spans": [{"bbox": [575, 170, 683, 190], "type": "text", "content": "云平台EIS架构"}]}], "index": 12, "level": 1}, {"type": "image", "bbox": [359, 196, 912, 505], "blocks": [{"bbox": [359, 196, 912, 505], "lines": [{"bbox": [359, 196, 912, 505], "spans": [{"bbox": [359, 196, 912, 505], "type": "image", "image_path": "df593421e8c167db65f5dff4b1d7b563eb5834ae646e8a3bfe2c67e3f996edfb.jpg"}]}], "index": 13, "type": "image_body"}], "index": 13}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 7}, {"para_blocks": [{"bbox": [18, 17, 181, 46], "type": "title", "lines": [{"bbox": [18, 17, 181, 46], "spans": [{"bbox": [18, 17, 181, 46], "type": "text", "content": "警务实训云平台"}]}], "index": 0, "level": 1}, {"bbox": [36, 91, 904, 142], "type": "title", "lines": [{"bbox": [36, 91, 904, 142], "spans": [{"bbox": [36, 91, 904, 142], "type": "text", "content": "云服务能力：云主机、GPU云主机、云主机镜像、云主机快照、云硬盘、云硬盘快照、对象存储、安全组、VPC、子网管理、EIP、容器、容器镜像、监控告警"}]}], "index": 1, "level": 1}, {"type": "table", "bbox": [33, 187, 480, 486], "blocks": [{"bbox": [33, 187, 480, 486], "lines": [{"bbox": [33, 187, 480, 486], "spans": [{"bbox": [33, 187, 480, 486], "type": "table", "html": "<table><tr><td>控制台</td><td>一级-云服务</td><td>二级</td></tr><tr><td rowspan=\"2\">云上</td><td>产品订购-云上</td><td>小站订购</td></tr><tr><td>本地小站信息</td><td>可查看本地小站信息</td></tr><tr><td rowspan=\"12\">本地控制台</td><td rowspan=\"2\">用户管理</td><td>用户管理</td></tr><tr><td>角色管理</td></tr><tr><td>云主机管理</td><td>云主机生命周期管理</td></tr><tr><td rowspan=\"2\">云硬盘管理</td><td>云硬盘生命周期管理</td></tr><tr><td>云硬盘快照</td></tr><tr><td rowspan=\"3\">网络管理</td><td>VPC生命周期管理</td></tr><tr><td>子网生命周期管理</td></tr><tr><td>EIP</td></tr><tr><td rowspan=\"2\">镜像管理</td><td>容器镜像管理</td></tr><tr><td>虚机镜像管理</td></tr><tr><td rowspan=\"2\">运维监控管理</td><td>虚机/容器监控</td></tr><tr><td>硬件监控</td></tr></table>", "image_path": "7b49f396d2ec41dd0e86e2a4292fdb6313d3462677e2f24d0e17b62dbf502bec.jpg"}]}], "index": 3, "type": "table_body"}, {"bbox": [42, 163, 142, 182], "lines": [{"bbox": [42, 163, 142, 182], "spans": [{"bbox": [42, 163, 142, 182], "type": "text", "content": "·主要功能："}]}], "index": 2, "type": "table_caption"}], "index": 3}, {"type": "table", "bbox": [503, 187, 930, 482], "blocks": [{"bbox": [503, 187, 930, 482], "lines": [{"bbox": [503, 187, 930, 482], "spans": [{"bbox": [503, 187, 930, 482], "type": "table", "html": "<table><tr><td>控制台</td><td>一级</td><td>二级</td></tr><tr><td rowspan=\"15\">本地控制台</td><td rowspan=\"3\">集群管理</td><td>命名空间管理</td></tr><tr><td>节点管理</td></tr><tr><td>IPV4/IPv6双栈支持</td></tr><tr><td rowspan=\"5\">存储管理</td><td>云硬盘管理</td></tr><tr><td>存储卷管理</td></tr><tr><td>云硬盘快照</td></tr><tr><td>存储卷快照</td></tr><tr><td>对象存储</td></tr><tr><td rowspan=\"6\">工作负载管理</td><td>无状态</td></tr><tr><td>有状态</td></tr><tr><td>任务</td></tr><tr><td>容器组</td></tr><tr><td>定时任务</td></tr><tr><td>守护进程</td></tr><tr><td>安全组</td><td>安全组管理</td></tr></table>", "image_path": "23ba03d420aed065873eee4a45c465c7d93d8a57285615397612bf89f0361691.jpg"}]}], "index": 4, "type": "table_body"}], "index": 4}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 8}, {"para_blocks": [{"bbox": [10, 17, 264, 47], "type": "title", "lines": [{"bbox": [10, 17, 264, 47], "spans": [{"bbox": [10, 17, 264, 47], "type": "text", "content": "5G小空间单警训练方舱"}]}], "index": 0, "level": 1}, {"bbox": [36, 71, 936, 121], "type": "text", "lines": [{"bbox": [36, 71, 936, 121], "spans": [{"bbox": [36, 71, 936, 121], "type": "text", "content": "\"VR\"警务实战训练方舱采用三维全景技术构建单兵训练场景的搭建和训练，根据人才培养目标与课程需求，编写课程脚本，构建不同的警情现场环境、案件的具体情节进行训练。该方舱通过5G互联，达成同一课程多人接入，可支持多人同时训练。"}]}], "index": 1}, {"bbox": [74, 138, 587, 193], "type": "text", "lines": [{"bbox": [74, 138, 587, 193], "spans": [{"bbox": [74, 138, 587, 193], "type": "text", "content": "VR小空间单警实训室，面积小，部署灵活可部署于基层派出所、县区局、市局，随时满足警务培训。"}]}], "index": 2}, {"type": "image", "bbox": [33, 199, 518, 469], "blocks": [{"bbox": [33, 199, 518, 469], "lines": [{"bbox": [33, 199, 518, 469], "spans": [{"bbox": [33, 199, 518, 469], "type": "image", "image_path": "3c136d91dda156884e9bc2f99594b9d23c921a2b45db17bf136c8ef69d03b71a.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [564, 212, 749, 304], "type": "text", "lines": [{"bbox": [564, 212, 749, 304], "spans": [{"bbox": [564, 212, 749, 304], "type": "text", "content": "9平方用最小的空间干最多的事最低仅需9平方即可"}]}], "index": 4}, {"bbox": [565, 355, 648, 391], "type": "text", "lines": [{"bbox": [565, 355, 648, 391], "spans": [{"bbox": [565, 355, 648, 391], "type": "text", "content": "80%"}]}], "index": 5}, {"bbox": [564, 400, 766, 451], "type": "text", "lines": [{"bbox": [564, 400, 766, 451], "spans": [{"bbox": [564, 400, 766, 451], "type": "text", "content": "覆盖80%以上通用训练场景满足基层警力使用"}]}], "index": 6}, {"bbox": [74, 488, 587, 517], "type": "text", "lines": [{"bbox": [74, 488, 587, 517], "spans": [{"bbox": [74, 488, 587, 517], "type": "text", "content": "碎片化时间轮训，基层警员不离岗，同一课程，多人接入"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 9}, {"para_blocks": [{"bbox": [10, 16, 336, 47], "type": "title", "lines": [{"bbox": [10, 16, 336, 47], "spans": [{"bbox": [10, 16, 336, 47], "type": "text", "content": "5G大空间多人协同作战实训室"}]}], "index": 0, "level": 1}, {"bbox": [31, 71, 936, 122], "type": "text", "lines": [{"bbox": [31, 71, 936, 122], "spans": [{"bbox": [31, 71, 936, 122], "type": "text", "content": "项目建成国内首个5G+VR警务多人大空间实训系统。通过VR设备连接5G- CPE后转至5G核心网并与警察学院数据中心交互，通过部署地市节点的GPU云主机进行VR图形计算减少终端渲染计算压力，使受训学员不受时空限制，可同时异地多人培训。"}]}], "index": 1}, {"type": "image", "bbox": [24, 185, 480, 397], "blocks": [{"bbox": [24, 185, 480, 397], "lines": [{"bbox": [24, 185, 480, 397], "spans": [{"bbox": [24, 185, 480, 397], "type": "image", "image_path": "3bb3b987b53bf9a081789c17f158f3605bf1e9b2be3a4a85c1c308823d9e975c.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}, {"bbox": [104, 447, 323, 496], "type": "text", "lines": [{"bbox": [104, 447, 323, 496], "spans": [{"bbox": [104, 447, 323, 496], "type": "text", "content": "中心云化部署，5G专网直达分支轻量部署，全省一张网"}]}], "index": 3}, {"bbox": [506, 150, 936, 226], "type": "text", "lines": [{"bbox": [506, 150, 936, 226], "spans": [{"bbox": [506, 150, 936, 226], "type": "text", "content": "通过搭建5G专网与警务专网VR设备连接，与部署在学校侧的5G+边缘云MEC进行数据交互，各地仅需部署警务VR终端即可通过5G专网接入实训系统，使得警务实训脱离时空限制"}]}], "index": 4}, {"bbox": [561, 255, 695, 278], "type": "title", "lines": [{"bbox": [561, 255, 695, 278], "spans": [{"bbox": [561, 255, 695, 278], "type": "text", "content": "打造省级软切片"}]}], "index": 5, "level": 1}, {"bbox": [564, 288, 750, 310], "type": "text", "lines": [{"bbox": [564, 288, 750, 310], "spans": [{"bbox": [564, 288, 750, 310], "type": "text", "content": "可实现跨地市协同训练"}]}], "index": 6}, {"bbox": [559, 349, 676, 369], "type": "text", "lines": [{"bbox": [559, 349, 676, 369], "spans": [{"bbox": [559, 349, 676, 369], "type": "text", "content": "多场景拟真解决"}]}], "index": 7}, {"bbox": [557, 382, 736, 404], "type": "text", "lines": [{"bbox": [557, 382, 736, 404], "spans": [{"bbox": [557, 382, 736, 404], "type": "text", "content": "剩余20%关键场景训练"}]}], "index": 8}, {"bbox": [614, 446, 849, 496], "type": "text", "lines": [{"bbox": [614, 446, 849, 496], "spans": [{"bbox": [614, 446, 849, 496], "type": "text", "content": "打破时空限制，多人团队协作远程实时控制，多个视角观战"}]}], "index": 9}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 10}, {"para_blocks": [{"bbox": [10, 17, 192, 46], "type": "title", "lines": [{"bbox": [10, 17, 192, 46], "spans": [{"bbox": [10, 17, 192, 46], "type": "text", "content": "警务VR实训终端"}]}], "index": 0, "level": 1}, {"bbox": [29, 71, 938, 145], "type": "text", "lines": [{"bbox": [29, 71, 938, 145], "spans": [{"bbox": [29, 71, 938, 145], "type": "text", "content": "项目根据警务实战的要求，自研1:1仿真VR警务终端，处VR头显、定位手套、体感设备外，同时开发了VR 92式手枪、VR 伸缩警棍、VR 防爆盾等警务设备，所有VR设备外观，重量，操作方式均与实际接出警警员所带装备保持一致，形成了集视觉、听觉与触觉三位一体的逼真虚拟实战环境，开创了警务训练的全新模式。"}]}], "index": 1}, {"type": "image", "bbox": [50, 171, 504, 430], "blocks": [{"bbox": [50, 171, 504, 430], "lines": [{"bbox": [50, 171, 504, 430], "spans": [{"bbox": [50, 171, 504, 430], "type": "image", "image_path": "bc2da37fd6352d4d9133452d55043af733850598501970954eda64d2711a70e3.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}, {"bbox": [175, 455, 361, 512], "type": "text", "lines": [{"bbox": [175, 455, 361, 512], "spans": [{"bbox": [175, 455, 361, 512], "type": "text", "content": "视觉+听觉+触觉三位一体"}]}], "index": 3}, {"type": "image", "bbox": [566, 167, 928, 516], "blocks": [{"bbox": [566, 167, 928, 516], "lines": [{"bbox": [566, 167, 928, 516], "spans": [{"bbox": [566, 167, 928, 516], "type": "image", "image_path": "34d086ff61224909833e30340ab147c344afbf47f549d26e678c9d842f8253de.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 11}, {"para_blocks": [{"bbox": [149, 253, 217, 289], "type": "title", "lines": [{"bbox": [149, 253, 217, 289], "spans": [{"bbox": [149, 253, 217, 289], "type": "text", "content": "目录"}]}], "index": 0, "level": 1}, {"bbox": [330, 165, 348, 184], "type": "text", "lines": [{"bbox": [330, 165, 348, 184], "spans": [{"bbox": [330, 165, 348, 184], "type": "text", "content": "1"}]}], "index": 1}, {"bbox": [474, 162, 669, 190], "type": "text", "lines": [{"bbox": [474, 162, 669, 190], "spans": [{"bbox": [474, 162, 669, 190], "type": "text", "content": "市场前景及需求分析"}]}], "index": 2}, {"bbox": [332, 286, 351, 306], "type": "text", "lines": [{"bbox": [332, 286, 351, 306], "spans": [{"bbox": [332, 286, 351, 306], "type": "text", "content": "2"}]}], "index": 3}, {"bbox": [528, 284, 616, 310], "type": "text", "lines": [{"bbox": [528, 284, 616, 310], "spans": [{"bbox": [528, 284, 616, 310], "type": "text", "content": "解决方案"}]}], "index": 4}, {"bbox": [155, 335, 205, 347], "type": "text", "lines": [{"bbox": [155, 335, 205, 347], "spans": [{"bbox": [155, 335, 205, 347], "type": "text", "content": "CONTENT"}]}], "index": 5}, {"bbox": [332, 404, 351, 423], "type": "text", "lines": [{"bbox": [332, 404, 351, 423], "spans": [{"bbox": [332, 404, 351, 423], "type": "text", "content": "3"}]}], "index": 6}, {"bbox": [528, 403, 616, 430], "type": "text", "lines": [{"bbox": [528, 403, 616, 430], "spans": [{"bbox": [528, 403, 616, 430], "type": "text", "content": "案例介绍"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 12}, {"para_blocks": [{"bbox": [31, 45, 134, 73], "type": "title", "lines": [{"bbox": [31, 45, 134, 73], "spans": [{"bbox": [31, 45, 134, 73], "type": "text", "content": "案例介绍"}]}], "index": 0, "level": 1}, {"bbox": [21, 85, 936, 157], "type": "text", "lines": [{"bbox": [21, 85, 936, 157], "spans": [{"bbox": [21, 85, 936, 157], "type": "text", "content": "- 河南警察学院5G+VR警务实训项目规划在警院内部署一个私有云中心作为中心云节点并部署虚拟实训平台；在警院内下沉UPF，部署数据专线与省级共享UPF相连；在18个地市部署一套边缘云，各节点部署5G网络切片。在本项目中主要使用了部分OneCyber平台、云XR，超级SIM卡、边缘GPU算力云、UPF下沉、5G双域专网、5G切片专网等能力。"}]}], "index": 1}, {"bbox": [450, 166, 509, 185], "type": "title", "lines": [{"bbox": [450, 166, 509, 185], "spans": [{"bbox": [450, 166, 509, 185], "type": "text", "content": "规划设计"}]}], "index": 2, "level": 1}, {"type": "table", "bbox": [52, 199, 439, 347], "blocks": [{"bbox": [52, 199, 439, 347], "lines": [{"bbox": [52, 199, 439, 347], "spans": [{"bbox": [52, 199, 439, 347], "type": "table", "html": "<table><tr><td>解决方案</td><td>能力展现</td></tr><tr><td>私有云化部署</td><td>为各省市警察学院提供本地化、私有化专有云，确保数据不出院区，提升校园网络安全能力</td></tr><tr><td>5G专享专网</td><td>UPF下沉校园核心机房，依托全网切片传输能力，数据不出网，安全可靠</td></tr><tr><td>虚拟实训平台</td><td>一套贯穿接警、出警、处警全流程的闭环训练平台、警察学院标准化课程</td></tr><tr><td>商务合作模式</td><td>中原豫资集团提供全国范围内的资金支持</td></tr></table>", "image_path": "019938f6a85b35cfd1cf8fd18613609d83b85db472e1b6bb7673566b12fddf69.jpg"}]}], "index": 3, "type": "table_body"}], "index": 3}, {"bbox": [458, 199, 540, 215], "type": "text", "lines": [{"bbox": [458, 199, 540, 215], "spans": [{"bbox": [458, 199, 540, 215], "type": "text", "content": "根据目前规划："}]}], "index": 4}, {"bbox": [456, 221, 917, 281], "type": "text", "lines": [{"bbox": [456, 221, 917, 281], "spans": [{"bbox": [456, 221, 917, 281], "type": "text", "content": "1、在警院内部署一个私有云中心作为中心云节点并部署虚拟实训平台；在警院下沉一个UPF，部署一条1G数据专线与省级共享UPF相连；在18个地市部署一套边缘云MEC，各节点部署5G网络切片 "}, {"bbox": [456, 221, 917, 281], "type": "inline_equation", "content": "30\\%"}]}], "index": 5}, {"bbox": [457, 286, 837, 346], "type": "text", "lines": [{"bbox": [457, 286, 837, 346], "spans": [{"bbox": [457, 286, 837, 346], "type": "text", "content": "2、地市单位每个5套单人方舱，2套多人大空间实训室；3、10A个县、县级市公安局每个3套单人方舱，1套多人大空间实训室；4、资金方面由中原豫资提供全国范围的支持，用以解决初期项目投入。"}]}], "index": 6}, {"bbox": [453, 355, 511, 372], "type": "title", "lines": [{"bbox": [453, 355, 511, 372], "spans": [{"bbox": [453, 355, 511, 372], "type": "text", "content": "商务模式"}]}], "index": 7, "level": 1}, {"bbox": [45, 382, 722, 530], "type": "text", "lines": [{"bbox": [45, 382, 722, 530], "spans": [{"bbox": [45, 382, 722, 530], "type": "text", "content": "1、由警察学院委托豫资东华（中原豫资下属子公司）作为联创实验室商业运作主体承接本项目。2、豫资东华以购买服务的形式将项目交由我方承建及运维。3、网络部分建设由我方投资建设并分年回收成本及收益。4、硬件、软件、平台部分由豫资东华作为垫资方在合同签订后、项目建设完成后分两笔支付。5、项目运维由豫资东华与我方另行签署协议实行。6、警校每年分期对项目费用进行支付。"}]}], "index": 8}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 13}, {"para_blocks": [{"bbox": [432, 225, 542, 271], "type": "text", "lines": [{"bbox": [432, 225, 542, 271], "spans": [{"bbox": [432, 225, 542, 271], "type": "text", "content": "谢谢！"}]}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 14}], "_backend": "vlm", "_version_name": "2.1.9"}