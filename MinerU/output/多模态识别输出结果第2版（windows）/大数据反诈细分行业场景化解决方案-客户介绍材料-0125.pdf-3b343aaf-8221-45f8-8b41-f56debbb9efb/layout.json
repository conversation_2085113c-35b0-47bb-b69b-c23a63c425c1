{"pdf_info": [{"para_blocks": [{"bbox": [188, 187, 770, 241], "type": "title", "lines": [{"bbox": [188, 187, 770, 241], "spans": [{"bbox": [188, 187, 770, 241], "type": "text", "content": "大数据反诈细分行业场景化解"}]}], "index": 0, "level": 1}, {"bbox": [266, 270, 692, 319], "type": "text", "lines": [{"bbox": [266, 270, 692, 319], "spans": [{"bbox": [266, 270, 692, 319], "type": "text", "content": "决方案- 客户介绍材料"}]}], "index": 1}, {"bbox": [429, 429, 532, 452], "type": "text", "lines": [{"bbox": [429, 429, 532, 452], "spans": [{"bbox": [429, 429, 532, 452], "type": "text", "content": "2024年1月"}]}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 0}, {"para_blocks": [{"type": "image", "bbox": [271, 130, 645, 484], "blocks": [{"bbox": [271, 130, 645, 484], "lines": [{"bbox": [271, 130, 645, 484], "spans": [{"bbox": [271, 130, 645, 484], "type": "image", "image_path": "2c76dc3e9c713a50f60362c3ba88544c4d6e76b97ff45962f26299b0f45fa22c.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 1}, {"para_blocks": [{"bbox": [53, 18, 225, 47], "type": "title", "lines": [{"bbox": [53, 18, 225, 47], "spans": [{"bbox": [53, 18, 225, 47], "type": "text", "content": "平台政策及背景"}]}], "index": 0, "level": 1}, {"bbox": [26, 70, 109, 88], "type": "title", "lines": [{"bbox": [26, 70, 109, 88], "spans": [{"bbox": [26, 70, 109, 88], "type": "text", "content": "平台背景"}]}], "index": 1, "level": 1}, {"bbox": [26, 91, 906, 176], "type": "text", "lines": [{"bbox": [26, 91, 906, 176], "spans": [{"bbox": [26, 91, 906, 176], "type": "text", "content": "平台背景近年来，电信网络诈骗活动呈多发高发态势，在刑事案件中占据很大比重，严重危害人民群众利益与社会稳定。广东是全国电信网络诈骗最严重的地区之一，电信网络诈骗治理工作形势严峻。随着电信网络诈骗向专业化、智能化、产业化、多样化发展，对基础电信运营企业的技术防范体系提出了更快、更准、更全的要求。以新型技术和网络为依托的各类新兴诈骗手段不断涌现，反侦察手段不断翻新、智能化，打击难度不断提升。"}]}], "index": 2}, {"bbox": [48, 192, 154, 210], "type": "title", "lines": [{"bbox": [48, 192, 154, 210], "spans": [{"bbox": [48, 192, 154, 210], "type": "text", "content": "2021年政策背景"}]}], "index": 3, "level": 1}, {"bbox": [42, 218, 432, 278], "type": "text", "lines": [{"bbox": [42, 218, 432, 278], "spans": [{"bbox": [42, 218, 432, 278], "type": "text", "content": "2021年政策背景2021年4月，习近平总书记对打击治理电信网络诈骗犯罪工作作出重要指示强调：坚持以人民为中心，全面落实打防管控措施，坚决遏制电信网络诈骗犯罪多发高发态势。"}]}], "index": 4}, {"bbox": [48, 300, 154, 316], "type": "title", "lines": [{"bbox": [48, 300, 154, 316], "spans": [{"bbox": [48, 300, 154, 316], "type": "text", "content": "2022年政策背景"}]}], "index": 5, "level": 1}, {"bbox": [40, 324, 437, 354], "type": "text", "lines": [{"bbox": [40, 324, 437, 354], "spans": [{"bbox": [40, 324, 437, 354], "type": "text", "content": "2022年9月，第十三届全国人大常委会第三十六次会议审议高票通过了《中华人民共和国反电信网络诈骗法》。"}]}], "index": 6}, {"bbox": [33, 354, 437, 412], "type": "text", "lines": [{"bbox": [33, 354, 437, 412], "spans": [{"bbox": [33, 354, 437, 412], "type": "text", "content": "2022年，工信部关于印发《2022年省级基础电信企业网络与信息安全工作考核要点与评分标准的通知》（工信厅网安函〔2022〕55号），防范治理电信网络诈骗技术手段的要求：部省反诈能力整合、诈骗防范系统建设和互联网反诈能力建设。"}]}], "index": 7}, {"bbox": [48, 436, 154, 454], "type": "title", "lines": [{"bbox": [48, 436, 154, 454], "spans": [{"bbox": [48, 436, 154, 454], "type": "text", "content": "2023年政策背景"}]}], "index": 8, "level": 1}, {"bbox": [42, 463, 438, 523], "type": "text", "lines": [{"bbox": [42, 463, 438, 523], "spans": [{"bbox": [42, 463, 438, 523], "type": "text", "content": "2023年，最高检发布《检察机关打击治理电信网络诈骗及其关联犯罪工作情况（2023年）》，分析当前电信网络诈骗主要态势，总结打击治理的举措与成效，利用数据赋能推动打击治理工作提质增效，协同推动电信网络诈骗综合治理。"}]}], "index": 9}, {"type": "image", "bbox": [443, 184, 557, 281], "blocks": [{"bbox": [443, 184, 557, 281], "lines": [{"bbox": [443, 184, 557, 281], "spans": [{"bbox": [443, 184, 557, 281], "type": "image", "image_path": "ac4b38787a6864a15c367b437056f8335a456521777ae9d32d75266cf08957e5.jpg"}]}], "index": 10, "type": "image_body"}], "index": 10}, {"type": "image", "bbox": [456, 308, 535, 412], "blocks": [{"bbox": [456, 308, 535, 412], "lines": [{"bbox": [456, 308, 535, 412], "spans": [{"bbox": [456, 308, 535, 412], "type": "image", "image_path": "c3ad560e84369ede50d09df7b848db3867ff76551f83f2846059616ee00b3276.jpg"}]}], "index": 11, "type": "image_body"}], "index": 11}, {"type": "image", "bbox": [443, 428, 557, 523], "blocks": [{"bbox": [443, 428, 557, 523], "lines": [{"bbox": [443, 428, 557, 523], "spans": [{"bbox": [443, 428, 557, 523], "type": "image", "image_path": "1245811542f736bca6854e8133e562568f5fd950060875cf986f62559e657690.jpg"}]}], "index": 12, "type": "image_body"}], "index": 12}, {"bbox": [706, 187, 775, 210], "type": "title", "lines": [{"bbox": [706, 187, 775, 210], "spans": [{"bbox": [706, 187, 775, 210], "type": "text", "content": "商机方向"}]}], "index": 13, "level": 1}, {"bbox": [585, 226, 728, 244], "type": "title", "lines": [{"bbox": [585, 226, 728, 244], "spans": [{"bbox": [585, 226, 728, 244], "type": "text", "content": "一、信息监测预警机制"}]}], "index": 14, "level": 1}, {"bbox": [585, 251, 901, 370], "type": "text", "lines": [{"bbox": [585, 251, 901, 370], "spans": [{"bbox": [585, 251, 901, 370], "type": "text", "content": "构建严密防范体系，强化技术反制，建立对涉诈网站、APP及诈骗电话、诈骗短消息处置机制；强化预警劝阻，不断提升预警信息监测发现能力，及时发现潜在受害群众，采取劝阻措施，提升监测、预警、处置一体化投放能力。"}]}], "index": 15}, {"bbox": [585, 378, 727, 395], "type": "title", "lines": [{"bbox": [585, 378, 727, 395], "spans": [{"bbox": [585, 378, 727, 395], "type": "text", "content": "二、事前反诈预警能力"}]}], "index": 16, "level": 1}, {"bbox": [585, 402, 902, 522], "type": "text", "lines": [{"bbox": [585, 402, 902, 522], "spans": [{"bbox": [585, 402, 902, 522], "type": "text", "content": "二、事前反诈预警能力打造信息通信行业反诈大数据技术手段，持续提升大数据技术管控水平，强化事前预防能力建设，建立全网疑似涉诈网络资源交叉核验机制，对高危码号、IP地址、域名等及时清理整顿，提早防范化解涉诈风险。"}]}], "index": 17}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 2}, {"para_blocks": [{"bbox": [53, 18, 225, 47], "type": "title", "lines": [{"bbox": [53, 18, 225, 47], "spans": [{"bbox": [53, 18, 225, 47], "type": "text", "content": "客户现状及需求"}]}], "index": 0, "level": 1}, {"bbox": [24, 67, 105, 84], "type": "title", "lines": [{"bbox": [24, 67, 105, 84], "spans": [{"bbox": [24, 67, 105, 84], "type": "text", "content": "现状分析"}]}], "index": 1, "level": 1}, {"bbox": [21, 88, 912, 130], "type": "text", "lines": [{"bbox": [21, 88, 912, 130], "spans": [{"bbox": [21, 88, 912, 130], "type": "text", "content": "电信网络诈骗案发中诈骗犯罪份子大多采取境外窝点，欺诈网站及欺诈电话为了逃避检测会频繁更换，且诈骗手段套路不断翻新，高频次、短周期的诈骗活动给人民群众带来巨大的威胁，造成严重的财产损失。"}]}], "index": 2}, {"bbox": [240, 145, 311, 166], "type": "title", "lines": [{"bbox": [240, 145, 311, 166], "spans": [{"bbox": [240, 145, 311, 166], "type": "text", "content": "公安痛点"}]}], "index": 3, "level": 1}, {"bbox": [733, 145, 804, 166], "type": "title", "lines": [{"bbox": [733, 145, 804, 166], "spans": [{"bbox": [733, 145, 804, 166], "type": "text", "content": "解决思路"}]}], "index": 4, "level": 1}, {"bbox": [40, 191, 504, 211], "type": "title", "lines": [{"bbox": [40, 191, 504, 211], "spans": [{"bbox": [40, 191, 504, 211], "type": "text", "content": "发现难：缺乏自主识别能力，涉诈资源更新频繁、难以事前发现涉诈网站或欺诈号码"}]}], "index": 5, "level": 1}, {"bbox": [718, 190, 819, 211], "type": "title", "lines": [{"bbox": [718, 190, 819, 211], "spans": [{"bbox": [718, 190, 819, 211], "type": "text", "content": "自主识别模型"}]}], "index": 6, "level": 1}, {"bbox": [140, 225, 485, 276], "type": "text", "lines": [{"bbox": [140, 225, 485, 276], "spans": [{"bbox": [140, 225, 485, 276], "type": "text", "content": "不具备事前发现和识别涉诈网站、欺诈电话等能力。- 往往在接到受害人报案后，才获得涉诈网站信息，信息已滞后。- 涉诈网站、欺诈电话更换域名后，无法持续追踪。"}]}], "index": 7}, {"bbox": [618, 220, 913, 288], "type": "text", "lines": [{"bbox": [618, 220, 913, 288], "spans": [{"bbox": [618, 220, 913, 288], "type": "text", "content": "面向涉诈网络资源，包括涉诈网站、欺诈电话，构建基于文本、图像和用户行为等多模态内容识别模型，自主研判及发现涉诈网站、欺诈电话及易受诈用户。模型准确率 "}, {"bbox": [618, 220, 913, 288], "type": "inline_equation", "content": ">90\\%"}]}], "index": 8}, {"type": "image", "bbox": [40, 223, 127, 286], "blocks": [{"bbox": [40, 223, 127, 286], "lines": [{"bbox": [40, 223, 127, 286], "spans": [{"bbox": [40, 223, 127, 286], "type": "image", "image_path": "29eaad00c94882a45d938517153c016924ced2a1488e912bdc8480b389fd8978.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [40, 300, 287, 320], "type": "title", "lines": [{"bbox": [40, 300, 287, 320], "spans": [{"bbox": [40, 300, 287, 320], "type": "text", "content": "处置慢：现有涉案网站处置流程长、时效慢"}]}], "index": 10, "level": 1}, {"bbox": [703, 299, 834, 320], "type": "title", "lines": [{"bbox": [703, 299, 834, 320], "spans": [{"bbox": [703, 299, 834, 320], "type": "text", "content": "涉案网址快速反制"}]}], "index": 11, "level": 1}, {"type": "image", "bbox": [31, 331, 127, 400], "blocks": [{"bbox": [31, 331, 127, 400], "lines": [{"bbox": [31, 331, 127, 400], "spans": [{"bbox": [31, 331, 127, 400], "type": "image", "image_path": "17bf2be87767e31b913801fcb2c98df7a0df6638ded05c17db725ef168aa4e5e.jpg"}]}], "index": 12, "type": "image_body"}], "index": 12}, {"bbox": [141, 330, 489, 400], "type": "text", "lines": [{"bbox": [141, 330, 489, 400], "spans": [{"bbox": [141, 330, 489, 400], "type": "text", "content": "处置流程长：地市公安 "}, {"bbox": [141, 330, 489, 400], "type": "inline_equation", "content": "\\gimel"}, {"bbox": [141, 330, 489, 400], "type": "text", "content": " 省公安厅 "}, {"bbox": [141, 330, 489, 400], "type": "inline_equation", "content": "\\rightharpoonup"}, {"bbox": [141, 330, 489, 400], "type": "text", "content": " 工信部 "}, {"bbox": [141, 330, 489, 400], "type": "inline_equation", "content": "\\gimel"}, {"bbox": [141, 330, 489, 400], "type": "text", "content": " 运营商集团 "}, {"bbox": [141, 330, 489, 400], "type": "inline_equation", "content": "\\rightharpoonup"}, {"bbox": [141, 330, 489, 400], "type": "text", "content": " 各省运营商，预计需要1周以上时间。处置时效慢：涉案网站从发现到各运营商进行封堵处置时效慢，审批环节较长，不能满足快速反制的实际工作需求。"}]}], "index": 13}, {"bbox": [619, 329, 910, 380], "type": "text", "lines": [{"bbox": [619, 329, 910, 380], "spans": [{"bbox": [619, 329, 910, 380], "type": "text", "content": "提供涉案网址上传接口，对接公安涉案网址数据，1天内快速完成涉案网址的监控及反制，处理时效提升7倍。"}]}], "index": 14}, {"bbox": [40, 412, 369, 431], "type": "title", "lines": [{"bbox": [40, 412, 369, 431], "spans": [{"bbox": [40, 412, 369, 431], "type": "text", "content": "事前反制难：缺少事前反制技术手段，人工劝阻效率低下"}]}], "index": 15, "level": 1}, {"bbox": [710, 411, 827, 431], "type": "title", "lines": [{"bbox": [710, 411, 827, 431], "spans": [{"bbox": [710, 411, 827, 431], "type": "text", "content": "全方位劝阻提醒"}]}], "index": 16, "level": 1}, {"type": "image", "bbox": [31, 435, 127, 522], "blocks": [{"bbox": [31, 435, 127, 522], "lines": [{"bbox": [31, 435, 127, 522], "spans": [{"bbox": [31, 435, 127, 522], "type": "image", "image_path": "2ee0f1a22bb6dff7926a14032fdbba307354177f70d2d4c9d1b7b7e72254a715.jpg"}]}], "index": 17, "type": "image_body"}], "index": 17}, {"bbox": [140, 443, 492, 512], "type": "text", "lines": [{"bbox": [140, 443, 492, 512], "spans": [{"bbox": [140, 443, 492, 512], "type": "text", "content": "事后处置：往往在接到受害人报案后，才对涉诈网站或欺诈电话进行处置，缺少事前反制手段。- 人工劝阻：地市公安在接到预警信息后，采用人工方式进行劝阻，成本高，效率低下。"}]}], "index": 18}, {"bbox": [618, 443, 912, 512], "type": "text", "lines": [{"bbox": [618, 443, 912, 512], "spans": [{"bbox": [618, 443, 912, 512], "type": "text", "content": "大数据识别潜在受害人，实时预警、高效劝阻提醒，结合移动闪信触达能力 "}, {"bbox": [618, 443, 912, 512], "type": "inline_equation", "content": "+\\mathsf{Al}"}, {"bbox": [618, 443, 912, 512], "type": "text", "content": " 智能语音外呼等多维度用户触达能力，对潜在受害人进行事前宣防和劝阻提醒，覆盖面广、成本低、效率高。"}]}], "index": 19}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 3}, {"para_blocks": [{"bbox": [52, 17, 308, 48], "type": "title", "lines": [{"bbox": [52, 17, 308, 48], "spans": [{"bbox": [52, 17, 308, 48], "type": "text", "content": "行业竞品分析-联通数盾"}]}], "index": 0, "level": 1}, {"bbox": [36, 71, 930, 169], "type": "text", "lines": [{"bbox": [36, 71, 930, 169], "spans": [{"bbox": [36, 71, 930, 169], "type": "text", "content": "- 联通反欺诈产品：基于用户入网的基础信息、通信行为、缴费行为、移动互联网行为、位置信息等数据，建立用户评级，辅助客户进行风险控制。利用挖掘算法等模型，找出不同维度行为的关联关系，紧密结合业务场景，提供综合反欺诈产品能力。- 产品形态：非标准化产品，具备整体解决方案能力，为客户提供定制化产品服务，实现周期较长。- 目标客户：金融、电商、地产和汽车销售等领域进行放贷、信用消费前的评估，辅助客户进行风险控制。"}]}], "index": 1}, {"type": "image", "bbox": [36, 184, 930, 523], "blocks": [{"bbox": [36, 184, 930, 523], "lines": [{"bbox": [36, 184, 930, 523], "spans": [{"bbox": [36, 184, 930, 523], "type": "image", "image_path": "999b78743ce5139c8f0106042bbd0138ad92d20cfa490e4193ea55723fe16906.jpg"}]}], "index": 2, "type": "image_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 4}, {"para_blocks": [{"bbox": [51, 17, 357, 48], "type": "title", "lines": [{"bbox": [51, 17, 357, 48], "spans": [{"bbox": [51, 17, 357, 48], "type": "text", "content": "行业竞品分析-电信反诈雷达"}]}], "index": 0, "level": 1}, {"bbox": [36, 76, 933, 119], "type": "text", "lines": [{"bbox": [36, 76, 933, 119], "spans": [{"bbox": [36, 76, 933, 119], "type": "text", "content": "电信反欺诈产品：反诈雷达聚焦金融行业推出的一款风控安全服务，基于中国电信骨干网资源优势，实现对互联网上活跃的赌博/诈骗网站的洞察，监测发现用于收款的涉赌涉诈银行卡号，助力银行建立风控策略，加强打击治理跨境赌博、电诈等相关工作。"}]}], "index": 1}, {"bbox": [40, 124, 688, 144], "type": "text", "lines": [{"bbox": [40, 124, 688, 144], "spans": [{"bbox": [40, 124, 688, 144], "type": "text", "content": "产品形态：标准化产品，聚焦涉赌涉诈账户实时管控，搭建跨运营商、跨金融机构的防赌反诈监测平台。"}]}], "index": 2}, {"bbox": [42, 150, 317, 168], "type": "text", "lines": [{"bbox": [42, 150, 317, 168], "spans": [{"bbox": [42, 150, 317, 168], "type": "text", "content": "目标客户：银行、金融监理机构、反诈中心"}]}], "index": 3}, {"type": "image", "bbox": [24, 178, 672, 528], "blocks": [{"bbox": [24, 178, 672, 528], "lines": [{"bbox": [24, 178, 672, 528], "spans": [{"bbox": [24, 178, 672, 528], "type": "image", "image_path": "b24e8e1ac3fab5bb1b1172762fa0c05d8f621425bddcee45b246f99c281e6843.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"type": "image", "bbox": [688, 192, 921, 501], "blocks": [{"bbox": [688, 192, 921, 501], "lines": [{"bbox": [688, 192, 921, 501], "spans": [{"bbox": [688, 192, 921, 501], "type": "image", "image_path": "9cb7ed81d4438ddabe8e50796bbc4962f927390376827608fff136ba349bcbb8.jpg"}]}], "index": 5, "type": "image_body"}], "index": 5}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 5}, {"para_blocks": [{"bbox": [51, 17, 405, 48], "type": "title", "lines": [{"bbox": [51, 17, 405, 48], "spans": [{"bbox": [51, 17, 405, 48], "type": "text", "content": "行业竞品分析-腾讯灵鲲反诈平台"}]}], "index": 0, "level": 1}, {"bbox": [36, 73, 930, 136], "type": "text", "lines": [{"bbox": [36, 73, 930, 136], "spans": [{"bbox": [36, 73, 930, 136], "type": "text", "content": "反欺诈产品：腾讯灵鲲反诈大数据预警平台，全球最大安全云库覆盖99%网民，十年黑产打击经验，海量存储与计算能力：每天处理8000亿条安全数据，累计存储200PB数据，调动近40万核的算力，秒级结果反馈。依赖为自身和庞大的生态伙伴圈，基于腾讯独立互联网资源和模型研判技术覆盖风险洞察、精准预警、劝阻防范和协同反制等四大主要场景。"}]}], "index": 1}, {"bbox": [40, 142, 751, 162], "type": "text", "lines": [{"bbox": [40, 142, 751, 162], "spans": [{"bbox": [40, 142, 751, 162], "type": "text", "content": "产品形态：标准化产品，数据源来自腾讯及其生态合作商，覆盖风险洞察、精准预警、劝阻防范和协同反制等功能。"}]}], "index": 2}, {"bbox": [40, 167, 228, 186], "type": "text", "lines": [{"bbox": [40, 167, 228, 186], "spans": [{"bbox": [40, 167, 228, 186], "type": "text", "content": "目标客户：反诈中心、公安局"}]}], "index": 3}, {"type": "image", "bbox": [66, 199, 890, 523], "blocks": [{"bbox": [66, 199, 890, 523], "lines": [{"bbox": [66, 199, 890, 523], "spans": [{"bbox": [66, 199, 890, 523], "type": "image", "image_path": "1d6b6c064e7401fe4e4192088a606de7e7711c787a18abc7ec0ced9a60e89cf9.jpg"}]}], "index": 4, "type": "image_body"}, {"bbox": [362, 181, 592, 199], "lines": [{"bbox": [362, 181, 592, 199], "spans": [{"bbox": [362, 181, 592, 199], "type": "text", "content": "打造一体化新型诈骗的防范治理能力"}]}], "index": 5, "type": "image_caption"}], "index": 4}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 6}, {"para_blocks": [{"bbox": [50, 17, 201, 48], "type": "title", "lines": [{"bbox": [50, 17, 201, 48], "spans": [{"bbox": [50, 17, 201, 48], "type": "text", "content": "竞品对标分析"}]}], "index": 0, "level": 1}, {"bbox": [36, 82, 930, 125], "type": "text", "lines": [{"bbox": [36, 82, 930, 125], "spans": [{"bbox": [36, 82, 930, 125], "type": "text", "content": "中国移动大数据反诈平台具备用户规模大（12亿+），网络渠道覆盖广（语音、短信、网站、APP、物联网卡），劝阻手段丰富（闪信、短信、智能语音外呼），简单易用（不受终端、APP、浏览器限制）等优势，具备快速复制推广的条件。"}]}], "index": 1}, {"type": "table", "bbox": [24, 150, 936, 517], "blocks": [{"bbox": [24, 150, 936, 517], "lines": [{"bbox": [24, 150, 936, 517], "spans": [{"bbox": [24, 150, 936, 517], "type": "table", "html": "<table><tr><td>产品</td><td>特色功能</td><td>优势分析</td><td>结论</td></tr><tr><td>中国移动大数据反诈产品</td><td>1.反诈态势分析\n2.智能研判欺诈电话/网站模型\n3.精准预警\n4.全方位实时劝阻提醒</td><td>1.用户规模大：移动用户9.7亿，宽带用户2.7亿，物联网连接超10亿\n2.模型准确性高，覆盖面广：覆盖电话、网站、APP、短信全链条诈骗流程，涵盖10+多类案件高发诈骗类型\n3.预警实时性高，功能全面\n4.劝阻手段丰富：闪信、短信、AI语音外呼等\n5.易用性高：终端用户无需安装客户端，不增加公安额外工作量</td><td>用户规模大、模型覆盖面广、功能全面、劝阻手段丰富、易用性高</td></tr><tr><td>中国联通数盾反欺诈产品</td><td>1.反诈数据分析\n2.涉诈模型实时监测\n3.企业评级\n4.个人用户评级</td><td>1.用户规模小：2.1亿\n2.模型覆盖面窄：模型主要针对涉诈网站及APP，缺少电话、短信等模型\n3.只预警，不主动劝阻提醒</td><td>用户规模小、模型覆盖面窄、不能主动劝阻</td></tr><tr><td>中国电信反诈雷达产品</td><td>1.赌博/诈骗网站的洞察\n2.赌博/诈骗模型输出\n3.赌博/诈骗模型实时监测</td><td>1.用户规模小：移动用户3.9亿，宽带用户1.8亿\n2.模型覆盖面窄：主要针对涉赌模型\n3.只预警，不主动劝阻提醒</td><td>用户规模小、模型覆盖面窄、不能主动劝阻</td></tr><tr><td>腾讯灵鲲反诈大数据预警平台</td><td>1.精准预警\n2.风险洞察\n3.劝阻防范\n4.AI劝阻</td><td>1.用户覆盖渠道受限：仅能监测使用腾讯产品的用户，对于电话欺诈、非腾讯浏览器、QQ、微信用户无法监测\n2.诈骗模型受限：涉诈网站模型丰富，但缺少电话、短信或非腾讯产品系用户模型</td><td>用户来源渠道限制大、诈骗模型覆盖不全面</td></tr></table>", "image_path": "955499b32bb802582d1b9e0f54cbbce98dd66c2819c940be10abda7983202801.jpg"}]}], "index": 2, "type": "table_body"}], "index": 2}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 7}, {"para_blocks": [{"type": "image", "bbox": [271, 130, 645, 484], "blocks": [{"bbox": [271, 130, 645, 484], "lines": [{"bbox": [271, 130, 645, 484], "spans": [{"bbox": [271, 130, 645, 484], "type": "image", "image_path": "8217c09ae8219c69103c7f48206b5186748ed9b8f6e33e440a7295c7cba31fe2.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 8}, {"para_blocks": [{"bbox": [52, 17, 261, 48], "type": "title", "lines": [{"bbox": [52, 17, 261, 48], "spans": [{"bbox": [52, 17, 261, 48], "type": "text", "content": "方案概述-平台架构"}]}], "index": 0, "level": 1}, {"bbox": [31, 71, 114, 90], "type": "title", "lines": [{"bbox": [31, 71, 114, 90], "spans": [{"bbox": [31, 71, 114, 90], "type": "text", "content": "平台架构"}]}], "index": 1, "level": 1}, {"bbox": [45, 96, 840, 138], "type": "text", "lines": [{"bbox": [45, 96, 840, 138], "spans": [{"bbox": [45, 96, 840, 138], "type": "text", "content": "智慧反诈平台基于移动实时数据流量，提供面向公共安全领域的网络反欺诈防治SaaS级服务，平台包括区域内反诈态势感知、分析、精准预警及触达等功能。"}]}], "index": 2}, {"type": "image", "bbox": [36, 141, 852, 537], "blocks": [{"bbox": [36, 141, 852, 537], "lines": [{"bbox": [36, 141, 852, 537], "spans": [{"bbox": [36, 141, 852, 537], "type": "image", "image_path": "67aa7f49986784d7ae6b470d0d2e0de38302c9a5c9d1cb9186e518ab33bdbf6d.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 9}, {"para_blocks": [{"bbox": [52, 18, 260, 47], "type": "title", "lines": [{"bbox": [52, 18, 260, 47], "spans": [{"bbox": [52, 18, 260, 47], "type": "text", "content": "方案概述-产品服务"}]}], "index": 0, "level": 1}, {"bbox": [45, 70, 917, 115], "type": "text", "lines": [{"bbox": [45, 70, 917, 115], "spans": [{"bbox": [45, 70, 917, 115], "type": "text", "content": "智慧反诈平台发挥网络大数据和精准触达渠道的优势，面向公安反诈部门提供”三个精准反诈劝阻服务 "}, {"bbox": [45, 70, 917, 115], "type": "inline_equation", "content": "^+"}, {"bbox": [45, 70, 917, 115], "type": "text", "content": " 一个反诈专业SaaS应用”组合服务，支撑公安反诈部门实现全链条纵深洞察、信息化反制。"}]}], "index": 1}, {"bbox": [53, 124, 530, 139], "type": "text", "lines": [{"bbox": [53, 124, 530, 139], "spans": [{"bbox": [53, 124, 530, 139], "type": "text", "content": "一个专业应用：针对反诈场景研发海量专业模型，为公安提供反诈场景的大数据分析工具。"}]}], "index": 2}, {"type": "image", "bbox": [103, 144, 453, 312], "blocks": [{"bbox": [103, 144, 453, 312], "lines": [{"bbox": [103, 144, 453, 312], "spans": [{"bbox": [103, 144, 453, 312], "type": "image", "image_path": "dfd3a21929b14a5a86cb8df90f0c791eb9a4bc0a48effa4c3efc933a37e5b763.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"type": "image", "bbox": [520, 144, 840, 309], "blocks": [{"bbox": [520, 144, 840, 309], "lines": [{"bbox": [520, 144, 840, 309], "spans": [{"bbox": [520, 144, 840, 309], "type": "image", "image_path": "08145ca11d45fc5b9efc00987ce7e2d46ffb14cd44bdb84909c7fcae016c83c3.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"bbox": [62, 321, 624, 337], "type": "text", "lines": [{"bbox": [62, 321, 624, 337], "spans": [{"bbox": [62, 321, 624, 337], "type": "text", "content": "三个精准反诈劝阻服务：提供涉诈网站劝阻提醒能力、欺诈电话劝阻提醒能力、AI智能外呼劝阻提醒能力。"}]}], "index": 5}, {"bbox": [52, 344, 153, 360], "type": "title", "lines": [{"bbox": [52, 344, 153, 360], "spans": [{"bbox": [52, 344, 153, 360], "type": "text", "content": "涉诈网站劝阻提醒"}]}], "index": 6, "level": 1}, {"bbox": [347, 347, 458, 362], "type": "title", "lines": [{"bbox": [347, 347, 458, 362], "spans": [{"bbox": [347, 347, 458, 362], "type": "text", "content": "AI智能外呼劝阻提醒"}]}], "index": 7, "level": 1}, {"bbox": [666, 347, 768, 363], "type": "title", "lines": [{"bbox": [666, 347, 768, 363], "spans": [{"bbox": [666, 347, 768, 363], "type": "text", "content": "欺诈电话劝阻提醒"}]}], "index": 8, "level": 1}, {"bbox": [42, 380, 160, 463], "type": "text", "lines": [{"bbox": [42, 380, 160, 463], "spans": [{"bbox": [42, 380, 160, 463], "type": "text", "content": "基于移动网络能力，结合涉诈网站识别能力及智能网络能力，当用户浏览涉诈网站，系统会根据预设策略向用户下发提醒短信，提醒用户存在欺诈风险。"}]}], "index": 9}, {"type": "image", "bbox": [170, 341, 297, 539], "blocks": [{"bbox": [170, 341, 297, 539], "lines": [{"bbox": [170, 341, 297, 539], "spans": [{"bbox": [170, 341, 297, 539], "type": "image", "image_path": "c6d9b225825d3970c07c54f3780e22f987c6f7a25f3826b6f31467c225053827.jpg"}]}], "index": 10, "type": "image_body"}], "index": 10}, {"bbox": [337, 386, 463, 471], "type": "text", "lines": [{"bbox": [337, 386, 463, 471], "spans": [{"bbox": [337, 386, 463, 471], "type": "text", "content": "基于移动网络能力，结合涉诈网站识别能力及智能网络能力，当反诈平台判断当前访问涉诈网站客户为高危易受诈用户时，向用户发起AI智能外呼劝阻，提高劝阻成功率。"}]}], "index": 11}, {"bbox": [650, 386, 777, 469], "type": "text", "lines": [{"bbox": [650, 386, 777, 469], "spans": [{"bbox": [650, 386, 777, 469], "type": "text", "content": "基于移动网络能力，结合欺诈电话识别能力及智能网络能力，当用户接听/拨打涉诈电话时，向用户实时下发提醒内信，提醒用户正在接听/拨打的号码存在欺诈风险。"}]}], "index": 12}, {"type": "image", "bbox": [797, 340, 917, 531], "blocks": [{"bbox": [797, 340, 917, 531], "lines": [{"bbox": [797, 340, 917, 531], "spans": [{"bbox": [797, 340, 917, 531], "type": "image", "image_path": "04056fe5031c873060c9097922f71b59b2acc0dd54f3b7de94a1c32817fab9fc.jpg"}]}], "index": 13, "type": "image_body"}], "index": 13}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 10}, {"para_blocks": [{"bbox": [51, 17, 364, 48], "type": "title", "lines": [{"bbox": [51, 17, 364, 48], "spans": [{"bbox": [51, 17, 364, 48], "type": "text", "content": "核心功能-反诈专业SaaS应用"}]}], "index": 0, "level": 1}, {"bbox": [36, 73, 144, 91], "type": "title", "lines": [{"bbox": [36, 73, 144, 91], "spans": [{"bbox": [36, 73, 144, 91], "type": "text", "content": "智慧反诈平台"}]}], "index": 1, "level": 1}, {"bbox": [31, 95, 917, 137], "type": "text", "lines": [{"bbox": [31, 95, 917, 137], "spans": [{"bbox": [31, 95, 917, 137], "type": "text", "content": "利用可视化技术将全省各地区诈骗数据及来源通过各种图表进行展示，实时对数据进行统计，将隐藏在数据中的信息以大屏的方式进行直观呈现，将涉诈网站数据、易受害人群画像及反诈成效等数据进行结合，帮助公安更好的对反诈态势进行评估分析。"}]}], "index": 2}, {"type": "image", "bbox": [24, 187, 587, 506], "blocks": [{"bbox": [24, 187, 587, 506], "lines": [{"bbox": [24, 187, 587, 506], "spans": [{"bbox": [24, 187, 587, 506], "type": "image", "image_path": "62419a2e480433ed79bf4620c6bed3bd51e3c62d977379e8fbb6a9abdb3e8503.jpg"}]}], "index": 3, "type": "image_body"}, {"bbox": [45, 164, 105, 180], "lines": [{"bbox": [45, 164, 105, 180], "spans": [{"bbox": [45, 164, 105, 180], "type": "text", "content": "平台界面"}]}], "index": 4, "type": "image_caption"}], "index": 3}, {"bbox": [616, 164, 676, 180], "type": "title", "lines": [{"bbox": [616, 164, 676, 180], "spans": [{"bbox": [616, 164, 676, 180], "type": "text", "content": "主要功能"}]}], "index": 5, "level": 1}, {"type": "image", "bbox": [631, 198, 683, 246], "blocks": [{"bbox": [631, 198, 683, 246], "lines": [{"bbox": [631, 198, 683, 246], "spans": [{"bbox": [631, 198, 683, 246], "type": "image", "image_path": "bf127694d0eec755cbc80afddd51eee31846be61339b065079aa136251ba1598.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}, {"bbox": [609, 259, 722, 275], "type": "title", "lines": [{"bbox": [609, 259, 722, 275], "spans": [{"bbox": [609, 259, 722, 275], "type": "text", "content": "反诈态势驾驶舱"}]}], "index": 7, "level": 1}, {"bbox": [607, 277, 722, 327], "type": "text", "lines": [{"bbox": [607, 277, 722, 327], "spans": [{"bbox": [607, 277, 722, 327], "type": "text", "content": "面向客户提供全省各地区诈骗数据及来源，将隐藏在数据中的信息以大屏的方式进行直观呈现"}]}], "index": 8}, {"type": "image", "bbox": [804, 198, 852, 246], "blocks": [{"bbox": [804, 198, 852, 246], "lines": [{"bbox": [804, 198, 852, 246], "spans": [{"bbox": [804, 198, 852, 246], "type": "image", "image_path": "9c007cf8d87d6839487c7d5d228d54fa7bbc25184edbbc1d47c13f6df05a56ff.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [794, 255, 861, 274], "type": "title", "lines": [{"bbox": [794, 255, 861, 274], "spans": [{"bbox": [794, 255, 861, 274], "type": "text", "content": "首页概览"}]}], "index": 10, "level": 1}, {"bbox": [768, 277, 887, 304], "type": "text", "lines": [{"bbox": [768, 277, 887, 304], "spans": [{"bbox": [768, 277, 887, 304], "type": "text", "content": "面向客户业务人员展示本区反诈预警数据概况"}]}], "index": 11}, {"type": "image", "bbox": [640, 366, 683, 404], "blocks": [{"bbox": [640, 366, 683, 404], "lines": [{"bbox": [640, 366, 683, 404], "spans": [{"bbox": [640, 366, 683, 404], "type": "image", "image_path": "565810d50bf1c34bcaaede897cccfc60bc713f8a99abcb8cf6efaeb18b978dab.jpg"}]}], "index": 12, "type": "image_body"}], "index": 12}, {"bbox": [631, 423, 700, 440], "type": "title", "lines": [{"bbox": [631, 423, 700, 440], "spans": [{"bbox": [631, 423, 700, 440], "type": "text", "content": "网诈感知"}]}], "index": 13, "level": 1}, {"bbox": [607, 443, 730, 469], "type": "text", "lines": [{"bbox": [607, 443, 730, 469], "spans": [{"bbox": [607, 443, 730, 469], "type": "text", "content": "针对高频涉诈网站、高危人群、访问区域进行分析；"}]}], "index": 14}, {"type": "image", "bbox": [797, 364, 842, 403], "blocks": [{"bbox": [797, 364, 842, 403], "lines": [{"bbox": [797, 364, 842, 403], "spans": [{"bbox": [797, 364, 842, 403], "type": "image", "image_path": "c27d11ed808656ae48e8f4c7116aca3574121f5265b41921198f3436ab8a22d2.jpg"}]}], "index": 15, "type": "image_body"}], "index": 15}, {"bbox": [779, 420, 880, 438], "type": "title", "lines": [{"bbox": [779, 420, 880, 438], "spans": [{"bbox": [779, 420, 880, 438], "type": "text", "content": "反诈效果评估"}]}], "index": 16, "level": 1}, {"bbox": [768, 442, 888, 490], "type": "text", "lines": [{"bbox": [768, 442, 888, 490], "spans": [{"bbox": [768, 442, 888, 490], "type": "text", "content": "对网诈涉诈网站、易受诈人数、提醒人数，发送闪信提醒数、劝阻成效等信息统计分析兵呈现"}]}], "index": 17}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 11}, {"para_blocks": [{"bbox": [51, 18, 357, 48], "type": "title", "lines": [{"bbox": [51, 18, 357, 48], "spans": [{"bbox": [51, 18, 357, 48], "type": "text", "content": "核心能力-网诈及时劝阻提醒"}]}], "index": 0, "level": 1}, {"bbox": [31, 75, 197, 93], "type": "title", "lines": [{"bbox": [31, 75, 197, 93], "spans": [{"bbox": [31, 75, 197, 93], "type": "text", "content": "涉诈网站精准劝阻提醒"}]}], "index": 1, "level": 1}, {"bbox": [29, 99, 914, 145], "type": "text", "lines": [{"bbox": [29, 99, 914, 145], "spans": [{"bbox": [29, 99, 914, 145], "type": "text", "content": "针对目前公安缺乏涉诈网站研判能力、缺少事前反制手段等问题，大数据反诈产品基于移动闪信触达能力，结合涉诈网站研判能力及闪信触达提醒能力，对涉诈网站进行实时监控，当用户浏览涉诈网站，系统会根据预设策略向用户下发闪信提醒，提醒用户存在欺诈风险。"}]}], "index": 2}, {"type": "image", "bbox": [45, 160, 485, 497], "blocks": [{"bbox": [45, 160, 485, 497], "lines": [{"bbox": [45, 160, 485, 497], "spans": [{"bbox": [45, 160, 485, 497], "type": "image", "image_path": "2afffcd423b6f1de649260f4ffcac9910a671ed46a5ede48f8ad16874caa5cec.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [573, 171, 757, 193], "type": "title", "lines": [{"bbox": [573, 171, 757, 193], "spans": [{"bbox": [573, 171, 757, 193], "type": "text", "content": "涉诈网站精准劝阻提醒"}]}], "index": 4, "level": 1}, {"bbox": [573, 201, 852, 496], "type": "text", "lines": [{"bbox": [573, 201, 852, 496], "spans": [{"bbox": [573, 201, 852, 496], "type": "text", "content": "1. 对于高风险网站准实时预警；2. 覆盖4/5G及家宽用户；3. 可配置的频次控制、总次数控制；4. 提醒内容可个性化定制；5. 支持上传黑网址；6. 支持查看提醒闪信发送结果7. 不良网址来源：- 公安上传；- 移动自研；"}]}], "index": 5}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 12}, {"para_blocks": [{"bbox": [51, 17, 357, 48], "type": "title", "lines": [{"bbox": [51, 17, 357, 48], "spans": [{"bbox": [51, 17, 357, 48], "type": "text", "content": "核心能力-电诈实时劝阻提醒"}]}], "index": 0, "level": 1}, {"bbox": [31, 71, 168, 90], "type": "title", "lines": [{"bbox": [31, 71, 168, 90], "spans": [{"bbox": [31, 71, 168, 90], "type": "text", "content": "欺诈电话实时提醒"}]}], "index": 1, "level": 1}, {"bbox": [26, 98, 917, 140], "type": "text", "lines": [{"bbox": [26, 98, 917, 140], "spans": [{"bbox": [26, 98, 917, 140], "type": "text", "content": "基于移动闪信触达能力，结合大数据反诈产品的欺诈电话识别能力及智能网络能力，对涉诈电话进行实时监控，当用户接听/拨打涉诈电话的同时，向用户实时下发提醒闪信，提醒用户正在接听/拨打的号码存在欺诈风险。"}]}], "index": 2}, {"type": "image", "bbox": [16, 147, 453, 517], "blocks": [{"bbox": [16, 147, 453, 517], "lines": [{"bbox": [16, 147, 453, 517], "spans": [{"bbox": [16, 147, 453, 517], "type": "image", "image_path": "7bf807bb7db819bedff67d29650ebc378b3d75f02b3d7a98feba1e3c7b6b1ddb.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [486, 164, 618, 182], "type": "title", "lines": [{"bbox": [486, 164, 618, 182], "spans": [{"bbox": [486, 164, 618, 182], "type": "text", "content": "欺诈电话闪信提醒："}]}], "index": 4, "level": 1}, {"bbox": [485, 189, 770, 284], "type": "text", "lines": [{"bbox": [485, 189, 770, 284], "spans": [{"bbox": [485, 189, 770, 284], "type": "text", "content": "1、移动自研欺诈号码识别模型；2、覆盖所有移动用户，1秒内即可弹出提醒；3、提醒内容可个性化定制；4、支持查看提醒闪信发送结果"}]}], "index": 5}, {"bbox": [486, 315, 558, 333], "type": "title", "lines": [{"bbox": [486, 315, 558, 333], "spans": [{"bbox": [486, 315, 558, 333], "type": "text", "content": "应用场景："}]}], "index": 6, "level": 1}, {"bbox": [485, 340, 904, 435], "type": "text", "lines": [{"bbox": [485, 340, 904, 435], "spans": [{"bbox": [485, 340, 904, 435], "type": "text", "content": "场景一：用户接听到欺诈号码来电，系统向用户实时下发霸屏提醒闪信，提醒用户正在接听的号码存在欺诈风险场景二：用户拨打欺诈号码，系统向用户实时下发霸屏提醒闪信，提醒用户正在拨打的号码存在欺诈风险"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 13}, {"para_blocks": [{"bbox": [51, 17, 333, 48], "type": "title", "lines": [{"bbox": [51, 17, 333, 48], "spans": [{"bbox": [51, 17, 333, 48], "type": "text", "content": "核心能力-AI智能外呼劝阻"}]}], "index": 0, "level": 1}, {"bbox": [31, 70, 212, 88], "type": "title", "lines": [{"bbox": [31, 70, 212, 88], "spans": [{"bbox": [31, 70, 212, 88], "type": "text", "content": "AI智能外呼劝阻提醒能力"}]}], "index": 1, "level": 1}, {"bbox": [31, 104, 285, 123], "type": "title", "lines": [{"bbox": [31, 104, 285, 123], "spans": [{"bbox": [31, 104, 285, 123], "type": "text", "content": "针对公安自有预警用户号码进行外呼提醒"}]}], "index": 2, "level": 1}, {"bbox": [29, 147, 455, 210], "type": "text", "lines": [{"bbox": [29, 147, 455, 210], "spans": [{"bbox": [29, 147, 455, 210], "type": "text", "content": "用户痛点需求分析：目前公安大多采用人工拨打电话的方式开展劝阻工作，效率低，人力成本高。通过AI机器人方式替代目前人工拨打电话劝阻工作，实现降本增效。"}]}], "index": 3}, {"bbox": [506, 104, 762, 123], "type": "title", "lines": [{"bbox": [506, 104, 762, 123], "spans": [{"bbox": [506, 104, 762, 123], "type": "text", "content": "针对平台识别的易受诈用户进行提醒劝阻"}]}], "index": 4, "level": 1}, {"bbox": [505, 147, 916, 189], "type": "text", "lines": [{"bbox": [505, 147, 916, 189], "spans": [{"bbox": [505, 147, 916, 189], "type": "text", "content": "用户痛点需求分析：在用户号码不出局的前提下，通过AI智能语音外呼，有效解决电话劝阻提醒的问题。"}]}], "index": 5}, {"type": "image", "bbox": [40, 230, 413, 528], "blocks": [{"bbox": [40, 230, 413, 528], "lines": [{"bbox": [40, 230, 413, 528], "spans": [{"bbox": [40, 230, 413, 528], "type": "image", "image_path": "d5d3e425ec6a100a02f57b2ba8ed7d2420429caf185a5b35bdaae754adb640fb.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}, {"type": "image", "bbox": [479, 244, 943, 514], "blocks": [{"bbox": [479, 244, 943, 514], "lines": [{"bbox": [479, 244, 943, 514], "spans": [{"bbox": [479, 244, 943, 514], "type": "image", "image_path": "c2423d9f4edac0ec599bf0d49c973e7e9c3605a7cf2de8d062fae224b9738a98.jpg"}]}], "index": 7, "type": "image_body"}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 14}, {"para_blocks": [{"bbox": [51, 18, 152, 47], "type": "title", "lines": [{"bbox": [51, 18, 152, 47], "spans": [{"bbox": [51, 18, 152, 47], "type": "text", "content": "核心价值"}]}], "index": 0, "level": 1}, {"bbox": [117, 81, 217, 102], "type": "title", "lines": [{"bbox": [117, 81, 217, 102], "spans": [{"bbox": [117, 81, 217, 102], "type": "text", "content": "警情全面压降"}]}], "index": 1, "level": 1}, {"bbox": [431, 81, 531, 102], "type": "title", "lines": [{"bbox": [431, 81, 531, 102], "spans": [{"bbox": [431, 81, 531, 102], "type": "text", "content": "离点精准打击"}]}], "index": 2, "level": 1}, {"bbox": [761, 81, 830, 102], "type": "title", "lines": [{"bbox": [761, 81, 830, 102], "spans": [{"bbox": [761, 81, 830, 102], "type": "text", "content": "精准劝阻"}]}], "index": 3, "level": 1}, {"bbox": [18, 112, 298, 283], "type": "text", "lines": [{"bbox": [18, 112, 298, 283], "spans": [{"bbox": [18, 112, 298, 283], "type": "text", "content": "警情全面压降- 广州花都区：环比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "12\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": " ，同比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "24\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": "- 东莞茶山镇：环比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "12\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": "- 东莞寮步镇：环比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "30\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": "- 增城永宁街道：环比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "44\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": " ，同比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "68\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": "- 增城新塘街道：环比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "38\\%"}, {"bbox": [18, 112, 298, 283], "type": "text", "content": " ，同比下降 "}, {"bbox": [18, 112, 298, 283], "type": "inline_equation", "content": "52\\%"}]}], "index": 4}, {"type": "table", "bbox": [23, 300, 313, 508], "blocks": [{"bbox": [23, 300, 313, 508], "lines": [{"bbox": [23, 300, 313, 508], "spans": [{"bbox": [23, 300, 313, 508], "type": "table", "html": "<table><tr><td rowspan=\"2\">镇街</td><td rowspan=\"2\">派出所</td><td colspan=\"3\">9月份</td></tr><tr><td>警情</td><td>同比</td><td>环比</td></tr><tr><td rowspan=\"8\">新塘镇</td><td>卫山</td><td>26</td><td>-23.5%</td><td>4.0%</td></tr><tr><td>沙村</td><td>17</td><td>-70.2%</td><td>-55.3%</td></tr><tr><td>大敦</td><td>9</td><td>-35.7%</td><td>-47.1%</td></tr><tr><td>甘东</td><td>10</td><td>-33.3%</td><td>0.0%</td></tr><tr><td>西宁</td><td>2</td><td>-84.6%</td><td>-81.8%</td></tr><tr><td>白石</td><td>7</td><td>-58.8%</td><td>0.0%</td></tr><tr><td>沙埔</td><td>4</td><td>-55.6%</td><td>-69.2%</td></tr><tr><td>合计</td><td>75</td><td>-52.8%</td><td>-38.0%</td></tr><tr><td rowspan=\"3\">永宁街</td><td>永宁</td><td>6</td><td>-81.3%</td><td>-60.0%</td></tr><tr><td>永新</td><td>10</td><td>-47.4%</td><td>-28.6%</td></tr><tr><td>合计</td><td>16</td><td>-68.6%</td><td>-44.8%</td></tr></table>", "image_path": "9759a20888f262867c51eb10b78fe5ef80db90263bfe7985717fc730307bfa65.jpg"}]}], "index": 5, "type": "table_body"}], "index": 5}, {"bbox": [343, 112, 619, 265], "type": "text", "lines": [{"bbox": [343, 112, 619, 265], "spans": [{"bbox": [343, 112, 619, 265], "type": "text", "content": "2023年，累计捣毁诈骗窝点1066个。收到省市政府单位、公安机关表扬信、感谢信55封。2023年6月，猫头鹰反诈平台自研云听模型获得工信部反诈通报表扬并推广。代表广东公司明星中台能力，参加2023年中国移动合作伙伴大会，面向全行业推广。"}]}], "index": 6}, {"type": "image", "bbox": [336, 301, 624, 508], "blocks": [{"bbox": [336, 301, 624, 508], "lines": [{"bbox": [336, 301, 624, 508], "spans": [{"bbox": [336, 301, 624, 508], "type": "image", "image_path": "e18b0db4099bb9556e6174a9dd2bca4e2f375c830e39710c243e69e9657aacf3.jpg"}]}], "index": 7, "type": "image_body"}], "index": 7}, {"bbox": [655, 116, 928, 266], "type": "text", "lines": [{"bbox": [655, 116, 928, 266], "spans": [{"bbox": [655, 116, 928, 266], "type": "text", "content": "自23年3月份在全省范围开展试商用以来，已在全省范围服务超过30个公安客户，系统累计识别超过200万条黑网址，累计向全省的易受诈移动用户发送提醒闪信超过2700万次，平均劝阻率超过 "}, {"bbox": [655, 116, 928, 266], "type": "inline_equation", "content": "70\\%"}, {"bbox": [655, 116, 928, 266], "type": "text", "content": " ，投放率为0，并有效压降辖区内网络电信诈骗案件发生率，获得客户的一致认可。"}]}], "index": 8}, {"type": "image", "bbox": [671, 326, 919, 508], "blocks": [{"bbox": [671, 326, 919, 508], "lines": [{"bbox": [671, 326, 919, 508], "spans": [{"bbox": [671, 326, 919, 508], "type": "image", "image_path": "fd0ed77c0493fb82d82a3487891d75e8e58fc860de1a3bcf1c3ecdd1e0b7a27a.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 15}, {"para_blocks": [{"bbox": [52, 17, 321, 48], "type": "title", "lines": [{"bbox": [52, 17, 321, 48], "spans": [{"bbox": [52, 17, 321, 48], "type": "text", "content": "平台专利、软著、荣誉等"}]}], "index": 0, "level": 1}, {"bbox": [31, 68, 921, 132], "type": "text", "lines": [{"bbox": [31, 68, 921, 132], "spans": [{"bbox": [31, 68, 921, 132], "type": "text", "content": "知识产权方面：大数据反诈方案的底层识别能力完全由广东公司自主研发，拥有全部自主产权。目前已获得5项专利、2项软著；外部认可方面：广东公司在2021、2022连续两年里获得全国GOIP打击成效省级运营商第一名，累计协助广东公安侦破超过1200个诈骗窝点；内部认可方面：获得集团公司2023反诈领域“领题破题合力攻坚”创新技术应用项目一等奖等6个奖项；"}]}], "index": 1}, {"bbox": [33, 190, 105, 212], "type": "title", "lines": [{"bbox": [33, 190, 105, 212], "spans": [{"bbox": [33, 190, 105, 212], "type": "text", "content": "5项专利"}]}], "index": 2, "level": 1}, {"bbox": [132, 147, 810, 254], "type": "text", "lines": [{"bbox": [132, 147, 810, 254], "spans": [{"bbox": [132, 147, 810, 254], "type": "text", "content": "- 【专利号：202210714323.1】一种基于轨迹相似度识别电信诈骗团伙的方法 GD2202006- 【专利号：202211282856.3】基于通信行为、XDR和位置数据的号码白名单生成方法及装置 GD2207009- 【专利号：202210469343.7】非法HTTPS网站识别方法、装置和电子设备- 【专利号：202111292065.4】基于页面跳转的非法网站识别方法及装置- 【专利号：202111122281.4】灰色网站识别方法及装置实审"}]}], "index": 3}, {"bbox": [34, 289, 105, 311], "type": "title", "lines": [{"bbox": [34, 289, 105, 311], "spans": [{"bbox": [34, 289, 105, 311], "type": "text", "content": "2项软著"}]}], "index": 4, "level": 1}, {"bbox": [132, 280, 570, 322], "type": "text", "lines": [{"bbox": [132, 280, 570, 322], "spans": [{"bbox": [132, 280, 570, 322], "type": "text", "content": "- 智慧执法平台- 电信诈骗反欺诈AI感知分析预警系统（智慧反诈平台）- 猎头鹰反电话欺诈分析系统（猎头鹰反诈系统）"}]}], "index": 5}, {"bbox": [33, 412, 105, 434], "type": "title", "lines": [{"bbox": [33, 412, 105, 434], "spans": [{"bbox": [33, 412, 105, 434], "type": "text", "content": "6个奖项"}]}], "index": 6, "level": 1}, {"bbox": [129, 350, 906, 503], "type": "text", "lines": [{"bbox": [129, 350, 906, 503], "spans": [{"bbox": [129, 350, 906, 503], "type": "text", "content": "- 获集团一等奖 2023年反诈领域“领题破题合理攻坚”创新技术应用项目《智慧反诈“全链路”识别与准确预警劝阻》- 获省公司二等奖 中国移动广东公司2023年度业务服务类创新成果《执法反诈识别预警和金融反欺诈创新应用》- 获省公司二等奖 中国移动广东公司2023年度科学技术类创新成果《智慧反诈平台“全链路”识别与反制技术方案研究》- 获省公司二等奖 中国移动广东公司2023年在岗技术革新《“不良网址研判与反制方案”助力信息化反诈》- 获省公司二等奖 中国移动广东公司2023年在岗技术革新《基于大数据对访问恶意网址的用户进行闪信提醒的能力》- 获省公司二等奖 中国移动广东公司2023年在岗技术革新《构建基于云网安能力的涉诈行为精准识别和构建反诈全链条阻断能力，助力执法部门精准反诈》"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 16}, {"para_blocks": [{"type": "image", "bbox": [271, 129, 645, 484], "blocks": [{"bbox": [271, 129, 645, 484], "lines": [{"bbox": [271, 129, 645, 484], "spans": [{"bbox": [271, 129, 645, 484], "type": "image", "image_path": "3c46fd28f65965a8ea72d5c716d703732a2ef27e46ad1f87758ddb6abf351811.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 17}, {"para_blocks": [{"bbox": [53, 18, 152, 46], "type": "title", "lines": [{"bbox": [53, 18, 152, 46], "spans": [{"bbox": [53, 18, 152, 46], "type": "text", "content": "应用场景"}]}], "index": 0, "level": 1}, {"bbox": [45, 80, 290, 102], "type": "title", "lines": [{"bbox": [45, 80, 290, 102], "spans": [{"bbox": [45, 80, 290, 102], "type": "text", "content": "场景一：反诈态势感知（公安）"}]}], "index": 1, "level": 1}, {"bbox": [45, 118, 522, 189], "type": "text", "lines": [{"bbox": [45, 118, 522, 189], "spans": [{"bbox": [45, 118, 522, 189], "type": "text", "content": "■客户痛点：缺乏有效的信息化手段支撑，无法全面及时掌握及了解辖区内用户受诈风险和情况，反诈宣传工作过于宽泛，不能有针对性的开展反诈精准宣防工作。"}]}], "index": 2}, {"bbox": [46, 195, 524, 266], "type": "text", "lines": [{"bbox": [46, 195, 524, 266], "spans": [{"bbox": [46, 195, 524, 266], "type": "text", "content": "■应用场景：基于移动实时数据流量，提供面向公共安全领域的网络反欺诈防治SaaS级服务，让客户清晰洞察辖区内反诈态势情况，助力公安客户有效开展精准宣防工作。"}]}], "index": 3}, {"type": "image", "bbox": [544, 105, 892, 267], "blocks": [{"bbox": [544, 105, 892, 267], "lines": [{"bbox": [544, 105, 892, 267], "spans": [{"bbox": [544, 105, 892, 267], "type": "image", "image_path": "88e400bcbc0ec50defae59df5804ecb6d81655d9b3fb1e7c8645fa553f5b389d.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"bbox": [45, 304, 345, 327], "type": "title", "lines": [{"bbox": [45, 304, 345, 327], "spans": [{"bbox": [45, 304, 345, 327], "type": "text", "content": "场景二：黑资源数据研判识别（公安）"}]}], "index": 5, "level": 1}, {"bbox": [45, 342, 520, 388], "type": "text", "lines": [{"bbox": [45, 342, 520, 388], "spans": [{"bbox": [45, 342, 520, 388], "type": "text", "content": "■客户痛点：不具备事前发现和识别黑资源数据能力，涉诈网站、欺诈电话更换域名后，无法持续追踪。"}]}], "index": 6}, {"bbox": [47, 393, 522, 488], "type": "text", "lines": [{"bbox": [47, 393, 522, 488], "spans": [{"bbox": [47, 393, 522, 488], "type": "text", "content": "■应用场景：基于广东移动用户上网日志数据、通话日志数据、短信日志数据等大数据能力，结合我省反诈研判模型能力，每日及时研判及识别出鲜活、准确的涉诈网址及欺诈号码，助力公安及时、准确的识别辖区内的易受诈用户。"}]}], "index": 7}, {"type": "image", "bbox": [544, 327, 893, 489], "blocks": [{"bbox": [544, 327, 893, 489], "lines": [{"bbox": [544, 327, 893, 489], "spans": [{"bbox": [544, 327, 893, 489], "type": "image", "image_path": "50ca39502968e976b984bbec410e17ebd5cd3f9ad7227bac53246e8581df091f.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 18}, {"para_blocks": [{"bbox": [52, 18, 152, 46], "type": "title", "lines": [{"bbox": [52, 18, 152, 46], "spans": [{"bbox": [52, 18, 152, 46], "type": "text", "content": "应用场景"}]}], "index": 0, "level": 1}, {"bbox": [44, 79, 328, 103], "type": "title", "lines": [{"bbox": [44, 79, 328, 103], "spans": [{"bbox": [44, 79, 328, 103], "type": "text", "content": "场景三：涉诈网址快速反制（公安）"}]}], "index": 1, "level": 1}, {"bbox": [45, 118, 522, 190], "type": "text", "lines": [{"bbox": [45, 118, 522, 190], "spans": [{"bbox": [45, 118, 522, 190], "type": "text", "content": "■客户痛点：缺乏对涉诈网址的识别能力，往往在案发后才发现涉诈网站，发现涉诈网站后处置时效慢，需要将提交网址给省厅- >工信部- >移动集团- >各省移动- >处置，通常需5- 7天，诈骗网站已经失效。"}]}], "index": 2}, {"bbox": [46, 195, 522, 270], "type": "text", "lines": [{"bbox": [46, 195, 522, 270], "spans": [{"bbox": [46, 195, 522, 270], "type": "text", "content": "■应用场景：面向各地公安提供涉诈网址上传功能，发现涉案网站后通过平台上传网址，经过稽核后立即生效，并对涉案网站进行监控，并对访问涉案网站的用户进行闪信/外呼提醒，防止用户再次受骗。"}]}], "index": 3}, {"type": "image", "bbox": [544, 93, 906, 280], "blocks": [{"bbox": [544, 93, 906, 280], "lines": [{"bbox": [544, 93, 906, 280], "spans": [{"bbox": [544, 93, 906, 280], "type": "image", "image_path": "81b78474fecfc2f919fc1dd25c5c653dcd0bdec7b0d135595283a838b819585c.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"bbox": [45, 304, 310, 327], "type": "title", "lines": [{"bbox": [45, 304, 310, 327], "spans": [{"bbox": [45, 304, 310, 327], "type": "text", "content": "场景四：全方位事前反制（公安）"}]}], "index": 5, "level": 1}, {"bbox": [45, 342, 520, 388], "type": "text", "lines": [{"bbox": [45, 342, 520, 388], "spans": [{"bbox": [45, 342, 520, 388], "type": "text", "content": "■客户痛点：缺乏手段对辖区内的用户进行事前反制，往往在用户报案后，再进行事后处置。"}]}], "index": 6}, {"bbox": [46, 393, 522, 489], "type": "text", "lines": [{"bbox": [46, 393, 522, 489], "spans": [{"bbox": [46, 393, 522, 489], "type": "text", "content": "■应用场景：提供全方位、多渠道的事前提醒手段，覆盖4/5G/家宽上网、电话及短信用户，提供网诈闪信提醒、电诈闪信提醒及AI语音外呼等多维事前提醒手段，从面上对辖区内的移动用户有针对性的精准提醒，降低辖区内移动用户的受诈风险。"}]}], "index": 7}, {"type": "image", "bbox": [559, 323, 885, 505], "blocks": [{"bbox": [559, 323, 885, 505], "lines": [{"bbox": [559, 323, 885, 505], "spans": [{"bbox": [559, 323, 885, 505], "type": "image", "image_path": "75bbb0bc7318a7b7c4e8fd7a6426ac6e0addf11755f441db8a1921adc3ff9332.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 19}, {"para_blocks": [{"bbox": [52, 80, 328, 102], "type": "title", "lines": [{"bbox": [52, 80, 328, 102], "spans": [{"bbox": [52, 80, 328, 102], "type": "text", "content": "场景五：精准GOIP窝点打击（公安）"}]}], "index": 0, "level": 1}, {"bbox": [45, 118, 518, 189], "type": "text", "lines": [{"bbox": [45, 118, 518, 189], "spans": [{"bbox": [45, 118, 518, 189], "type": "text", "content": "■客户痛点：电话诈骗案件持续高位频发，新型GoIP电诈具备人卡分离、篡改号码和IP地址，对接猫池提高呼叫效率等特点成为诈骗“新宠”。公安缺少有效手段快速甄别。"}]}], "index": 1}, {"bbox": [47, 195, 522, 266], "type": "text", "lines": [{"bbox": [47, 195, 522, 266], "spans": [{"bbox": [47, 195, 522, 266], "type": "text", "content": "■应用场景：截至2023年4月18日，全省配合公安机关捣毁GOIP诈骗窝点222个，自开展2023年“打猫行动”以来，打击窝点数、缴获设备数均排全国省级运营商第一。"}]}], "index": 2}, {"type": "image", "bbox": [552, 97, 892, 273], "blocks": [{"bbox": [552, 97, 892, 273], "lines": [{"bbox": [552, 97, 892, 273], "spans": [{"bbox": [552, 97, 892, 273], "type": "image", "image_path": "aa29eb0aef0973355c7e0a129bd7cdb481462e7f9989be1696b4e43d9f28d3e0.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"bbox": [45, 304, 327, 327], "type": "title", "lines": [{"bbox": [45, 304, 327, 327], "spans": [{"bbox": [45, 304, 327, 327], "type": "text", "content": "场景六：境外诈骗短信提醒（公安）"}]}], "index": 4, "level": 1}, {"bbox": [47, 342, 516, 388], "type": "text", "lines": [{"bbox": [47, 342, 516, 388], "spans": [{"bbox": [47, 342, 516, 388], "type": "text", "content": "■客户痛点：诈骗份子多以境外端口方式向辖区内的用户撒网发送诈骗短信，诱导用户并实施诈骗行为，公安客户缺乏有效手段对此行为进行反制。"}]}], "index": 5}, {"type": "image", "bbox": [518, 318, 893, 501], "blocks": [{"bbox": [518, 318, 893, 501], "lines": [{"bbox": [518, 318, 893, 501], "spans": [{"bbox": [518, 318, 893, 501], "type": "image", "image_path": "f67e3e399fc2af20a165c2e56e017047607dffce38f9f12551fa7920da91e365.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}, {"bbox": [47, 418, 520, 463], "type": "text", "lines": [{"bbox": [47, 418, 520, 463], "spans": [{"bbox": [47, 418, 520, 463], "type": "text", "content": "■应用场景：平台提供境外短信提醒能力，针对辖区内接收到境外短信的移动用户，实时发下提醒闪信，降低辖区内移动用户的受骗风险。"}]}], "index": 7}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 20}, {"para_blocks": [{"type": "image", "bbox": [271, 130, 645, 484], "blocks": [{"bbox": [271, 130, 645, 484], "lines": [{"bbox": [271, 130, 645, 484], "spans": [{"bbox": [271, 130, 645, 484], "type": "image", "image_path": "7f4362f8aec7b56e05bdd99b304b6a429f5ab8dfa32c8cda6236f42c05865052.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 21}, {"para_blocks": [{"bbox": [53, 17, 284, 48], "type": "title", "lines": [{"bbox": [53, 17, 284, 48], "spans": [{"bbox": [53, 17, 284, 48], "type": "text", "content": "合作模式-试用转商用"}]}], "index": 0, "level": 1}, {"bbox": [23, 78, 417, 99], "type": "title", "lines": [{"bbox": [23, 78, 417, 99], "spans": [{"bbox": [23, 78, 417, 99], "type": "text", "content": "口合作模式：产品销售商务模式以试用转商用模式为主"}]}], "index": 1, "level": 1}, {"bbox": [66, 105, 941, 201], "type": "text", "lines": [{"bbox": [66, 105, 941, 201], "spans": [{"bbox": [66, 105, 941, 201], "type": "text", "content": "平台推广：引导省市区县公安厅局，向公安反诈部门进行推广，针对全省业务可以基于省级、市级、区县、街道四层分级架构，由省公司牵头整体布局和卡位。行业辐射：基于智慧反诈平台的切入，各项产品功能可向区域内的各级公安部门辐射，主体产品和增值产品可在区域内落地，按服务标准给与折扣后收费。"}]}], "index": 2}, {"type": "image", "bbox": [64, 216, 930, 514], "blocks": [{"bbox": [64, 216, 930, 514], "lines": [{"bbox": [64, 216, 930, 514], "spans": [{"bbox": [64, 216, 930, 514], "type": "image", "image_path": "9a24d5469f292455a122ca88309d8772d5d29af2a543c396bade56ace6dab765.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 22}, {"para_blocks": [{"bbox": [53, 17, 261, 48], "type": "title", "lines": [{"bbox": [53, 17, 261, 48], "spans": [{"bbox": [53, 17, 261, 48], "type": "text", "content": "合作模式-产品资费"}]}], "index": 0, "level": 1}, {"type": "table", "bbox": [24, 109, 933, 456], "blocks": [{"bbox": [24, 109, 933, 456], "lines": [{"bbox": [24, 109, 933, 456], "spans": [{"bbox": [24, 109, 933, 456], "type": "table", "html": "<table><tr><td>服务包类型</td><td>产品名称</td><td>产品服务内容及规格</td><td>销售底价</td><td>建议目录价</td><td>内部结算价</td></tr><tr><td rowspan=\"3\">智慧反诈基础服务</td><td>反诈专线</td><td>反诈专线（数据专线）提供智慧反诈平台与客户点对点直接，需要订购数据专线才可订购平台SaaS应用模块。</td><td>根据引入省双线产品资费</td><td>根据引入省双线产品资费</td><td>-</td></tr><tr><td>智慧反诈平台-网诈电诈模块</td><td>提供智慧反诈平台的反诈预警、网诈感知、电诈溯源等模块（产品服务周期为1年）</td><td>1200元/万人次/年</td><td>12000元/万人次/年</td><td>-</td></tr><tr><td>智慧反诈平台-反诈劝阻提醒模块</td><td>提供智慧反诈平台的反诈精准劝阻等模块（产品服务周期为1年）</td><td>260元/万人次/年</td><td>2600元/万人次/年</td><td>-</td></tr><tr><td rowspan=\"3\">智慧反诈增值服务</td><td>智慧反诈平台-易诈人员劝阻提醒服务</td><td>基于网络数据分析能力，向反诈的目标客户群提供基于平台网诈电诈研判的易诈人员劝阻提醒服务。</td><td>0.25元/条</td><td>1元/条</td><td>-</td></tr><tr><td>智慧反诈平台-智能AI外呼提醒服务</td><td>提供智能AI外呼提醒服务，支持带变量、语音交互类外呼，包含一次外呼模板定制。</td><td>20000元/年（1个并发）</td><td>80000元/年（1个并发）</td><td>22000元/年（1个并发）</td></tr><tr><td>智慧反诈平台-智能IVR外呼提醒服务</td><td>提供智能IVR外呼提醒服务，包含50000分钟/月。</td><td>40000元/年</td><td>200000元/年</td><td>44000元/年</td></tr></table>", "image_path": "a7c32fd3a5b25913c6e6e4d77572dfd9c8a25723c99fffb6efa098f56899359b.jpg"}]}], "index": 2, "type": "table_body"}, {"bbox": [36, 73, 575, 96], "lines": [{"bbox": [36, 73, 575, 96], "spans": [{"bbox": [36, 73, 575, 96], "type": "text", "content": "产品资费：按照预估成本及测算模型测算和成本定价原则，产品定价如下："}]}], "index": 1, "type": "table_caption"}], "index": 2}, {"bbox": [24, 461, 912, 523], "type": "text", "lines": [{"bbox": [24, 461, 912, 523], "spans": [{"bbox": [24, 461, 912, 523], "type": "text", "content": "智慧反诈平台- 网诈电诈模块、反诈劝阻提醒模块资费计算公式：目标客户（公安）每年资费=建议目录价*客户(公安)管辖区域人口数*折扣注：根据《中国移动大数据业务管理办法v5.0》如突破销售底价或季度营销方案，需提供生产单位内部请示，通过大数据工单评审；如合同金额突破100万，进行大数据需求评审会评审。"}]}], "index": 3}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 23}, {"para_blocks": [{"type": "image", "bbox": [271, 130, 645, 487], "blocks": [{"bbox": [271, 130, 645, 487], "lines": [{"bbox": [271, 130, 645, 487], "spans": [{"bbox": [271, 130, 645, 487], "type": "image", "image_path": "261215852d5d6c17ef5f4d19b0c3a52ad6bf0df0ac3eb02778cb2600972824c7.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 24}, {"para_blocks": [{"bbox": [52, 18, 152, 46], "type": "title", "lines": [{"bbox": [52, 18, 152, 46], "spans": [{"bbox": [52, 18, 152, 46], "type": "text", "content": "落地案例"}]}], "index": 0, "level": 1}, {"bbox": [42, 74, 917, 120], "type": "text", "lines": [{"bbox": [42, 74, 917, 120], "spans": [{"bbox": [42, 74, 917, 120], "type": "text", "content": "已在全省范围服务超过30个客户，累计识别超过170万条黑网址，识别欺诈号码超过2万个，累计向全省的易受诈移动用户发送提醒闪信超过2400万次，平均劝阻率达到 "}, {"bbox": [42, 74, 917, 120], "type": "inline_equation", "content": "70\\%"}, {"bbox": [42, 74, 917, 120], "type": "text", "content": " 以上，平台反诈成效获得公安客户普遍认可，部分客户有成效数据反馈；"}]}], "index": 1}, {"bbox": [48, 126, 359, 144], "type": "text", "lines": [{"bbox": [48, 126, 359, 144], "spans": [{"bbox": [48, 126, 359, 144], "type": "text", "content": "智慧反诈平台30个客户中，公安客户占比近 "}, {"bbox": [48, 126, 359, 144], "type": "inline_equation", "content": "90\\%"}]}], "index": 2}, {"bbox": [206, 149, 284, 171], "type": "title", "lines": [{"bbox": [206, 149, 284, 171], "spans": [{"bbox": [206, 149, 284, 171], "type": "text", "content": "推广成效"}]}], "index": 3, "level": 1}, {"bbox": [644, 149, 756, 171], "type": "title", "lines": [{"bbox": [644, 149, 756, 171], "spans": [{"bbox": [644, 149, 756, 171], "type": "text", "content": "网址研判成效"}]}], "index": 4, "level": 1}, {"bbox": [66, 188, 182, 295], "type": "text", "lines": [{"bbox": [66, 188, 182, 295], "spans": [{"bbox": [66, 188, 182, 295], "type": "text", "content": "服务客户：30个  市级单位：6个  区级单位：13个  镇级/街道级单位：11个  合同签订：2个，38.8万"}]}], "index": 5}, {"type": "image", "bbox": [256, 178, 448, 326], "blocks": [{"bbox": [256, 178, 448, 326], "lines": [{"bbox": [256, 178, 448, 326], "spans": [{"bbox": [256, 178, 448, 326], "type": "image", "image_path": "c93ab7ffbe146e3b68fa1f8f6c5cddf81f4094bc17bb409fd83dc6bc697caf08.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}, {"bbox": [535, 188, 681, 229], "type": "text", "lines": [{"bbox": [535, 188, 681, 229], "spans": [{"bbox": [535, 188, 681, 229], "type": "text", "content": "黑库累计：200万+  日均新增黑网址：2万+"}]}], "index": 7}, {"type": "image", "bbox": [549, 235, 674, 327], "blocks": [{"bbox": [549, 235, 674, 327], "lines": [{"bbox": [549, 235, 674, 327], "spans": [{"bbox": [549, 235, 674, 327], "type": "image", "image_path": "271cd89e84541291999ee6b7ee83265c9ac138d7b8ce487c94f434f5d2b80321.jpg"}]}], "index": 8, "type": "image_body"}], "index": 8}, {"type": "image", "bbox": [727, 184, 906, 328], "blocks": [{"bbox": [727, 184, 906, 328], "lines": [{"bbox": [727, 184, 906, 328], "spans": [{"bbox": [727, 184, 906, 328], "type": "image", "image_path": "df75fdda2b36b18466266f65c955591b1fd3acb3ac38bf5c0c5ca8219ab868da.jpg"}]}], "index": 9, "type": "image_body"}], "index": 9}, {"bbox": [189, 343, 302, 366], "type": "title", "lines": [{"bbox": [189, 343, 302, 366], "spans": [{"bbox": [189, 343, 302, 366], "type": "text", "content": "警情压降成效"}]}], "index": 10, "level": 1}, {"bbox": [644, 344, 756, 366], "type": "title", "lines": [{"bbox": [644, 344, 756, 366], "spans": [{"bbox": [644, 344, 756, 366], "type": "text", "content": "劝阻提醒成效"}]}], "index": 11, "level": 1}, {"bbox": [66, 374, 251, 520], "type": "text", "lines": [{"bbox": [66, 374, 251, 520], "spans": [{"bbox": [66, 374, 251, 520], "type": "text", "content": "广州花都区：环比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "12\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": " ，同比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "24\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": "  东莞茶山镇：环比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "12\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": "  东莞寮步镇：环比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "30\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": "  增城永宁街道：环比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "44\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": " ，同比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "68\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": "  增城新塘街道：环比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "38\\%"}, {"bbox": [66, 374, 251, 520], "type": "text", "content": " ，同比下降 "}, {"bbox": [66, 374, 251, 520], "type": "inline_equation", "content": "52\\%"}]}], "index": 12}, {"type": "image", "bbox": [251, 403, 437, 504], "blocks": [{"bbox": [251, 403, 437, 504], "lines": [{"bbox": [251, 403, 437, 504], "spans": [{"bbox": [251, 403, 437, 504], "type": "image", "image_path": "d4b9b4801bf05594b4bf09647ec680ec2427ba26c5f70a47272f78a4e938143d.jpg"}]}], "index": 13, "type": "image_body"}], "index": 13}, {"bbox": [528, 392, 755, 474], "type": "text", "lines": [{"bbox": [528, 392, 755, 474], "spans": [{"bbox": [528, 392, 755, 474], "type": "text", "content": "累计劝阻提醒：2000万 "}, {"bbox": [528, 392, 755, 474], "type": "inline_equation", "content": "^+"}, {"bbox": [528, 392, 755, 474], "type": "text", "content": " （人次）日均劝阻提醒：20万 "}, {"bbox": [528, 392, 755, 474], "type": "inline_equation", "content": "^+"}, {"bbox": [528, 392, 755, 474], "type": "text", "content": " （人次）劝阻成功率：超过 "}, {"bbox": [528, 392, 755, 474], "type": "inline_equation", "content": "70\\%"}, {"bbox": [528, 392, 755, 474], "type": "text", "content": " 报案用户提醒覆盖率：超 "}, {"bbox": [528, 392, 755, 474], "type": "inline_equation", "content": "50\\%"}]}], "index": 14}, {"type": "image", "bbox": [804, 358, 900, 511], "blocks": [{"bbox": [804, 358, 900, 511], "lines": [{"bbox": [804, 358, 900, 511], "spans": [{"bbox": [804, 358, 900, 511], "type": "image", "image_path": "1bdc498131c567d9c3c34e5e6b6ea2dbd96ef38ac14f78fe33777e7ea1ba96e8.jpg"}]}], "index": 15, "type": "image_body"}], "index": 15}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 25}, {"para_blocks": [{"bbox": [51, 18, 258, 47], "type": "title", "lines": [{"bbox": [51, 18, 258, 47], "spans": [{"bbox": [51, 18, 258, 47], "type": "text", "content": "落地案例-公安客户"}]}], "index": 0, "level": 1}, {"bbox": [42, 75, 917, 170], "type": "text", "lines": [{"bbox": [42, 75, 917, 170], "spans": [{"bbox": [42, 75, 917, 170], "type": "text", "content": "广州市花都区公安分局反诈平台试用从4月18日- 7月15日，覆盖区域为花都区全区，闪信发送上限30000条/日，单用户每天提醒1次。反诈平台试用期间，警情环比下降 "}, {"bbox": [42, 75, 917, 170], "type": "inline_equation", "content": "12\\%"}, {"bbox": [42, 75, 917, 170], "type": "text", "content": " ，同比下降 "}, {"bbox": [42, 75, 917, 170], "type": "inline_equation", "content": "24\\%"}, {"bbox": [42, 75, 917, 170], "type": "text", "content": " ，成效显著。东莞市寮步公安分局反诈平台试用从8月15日- 10月19日，覆盖区域为寮步镇全镇，闪信发送上限20000条/日，单用户每天提醒1次。反诈平台试用期间，警情环比下降 "}, {"bbox": [42, 75, 917, 170], "type": "inline_equation", "content": "30\\%"}, {"bbox": [42, 75, 917, 170], "type": "text", "content": " ，成效显著。"}]}], "index": 1}, {"bbox": [40, 176, 917, 226], "type": "text", "lines": [{"bbox": [40, 176, 917, 226], "spans": [{"bbox": [40, 176, 917, 226], "type": "text", "content": "东莞市茶山公安分局反诈平台试用从9月8日- 11月8日，覆盖区域为寮步镇全镇，闪信发送上限30000条/日，单用户每天提醒1次。反诈平台试用期间，警情环比下降 "}, {"bbox": [40, 176, 917, 226], "type": "inline_equation", "content": "12\\%"}, {"bbox": [40, 176, 917, 226], "type": "text", "content": " ，成效显著。 智慧反诈平台-寮步-协作群（6）"}]}], "index": 2}, {"type": "image", "bbox": [29, 230, 215, 525], "blocks": [{"bbox": [29, 230, 215, 525], "lines": [{"bbox": [29, 230, 215, 525], "spans": [{"bbox": [29, 230, 215, 525], "type": "image", "image_path": "20b5cc2ed65d1afee77a342e064acd185c2b8ef227697a22c9c785cac899c018.jpg"}]}], "index": 3, "type": "image_body"}], "index": 3}, {"type": "image", "bbox": [234, 231, 395, 525], "blocks": [{"bbox": [234, 231, 395, 525], "lines": [{"bbox": [234, 231, 395, 525], "spans": [{"bbox": [234, 231, 395, 525], "type": "image", "image_path": "6ead19c23f722708a1861530b7c1e62f442f7dcc541a5f025694bd1fee8a9ed5.jpg"}]}], "index": 4, "type": "image_body"}], "index": 4}, {"type": "image", "bbox": [426, 216, 633, 529], "blocks": [{"bbox": [426, 216, 633, 529], "lines": [{"bbox": [426, 216, 633, 529], "spans": [{"bbox": [426, 216, 633, 529], "type": "image", "image_path": "e1c26b5ceb203c370c6549e49866c234d81234f118e42ef268a037e1456d3072.jpg"}]}], "index": 5, "type": "image_body"}], "index": 5}, {"type": "image", "bbox": [686, 207, 904, 534], "blocks": [{"bbox": [686, 207, 904, 534], "lines": [{"bbox": [686, 207, 904, 534], "spans": [{"bbox": [686, 207, 904, 534], "type": "image", "image_path": "6641e7724a78a67fb9b31b28f65b2e800876f8625b587ae605a99b74ae2b8cb9.jpg"}]}], "index": 6, "type": "image_body"}], "index": 6}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 26}, {"para_blocks": [{"bbox": [52, 17, 258, 48], "type": "title", "lines": [{"bbox": [52, 17, 258, 48], "spans": [{"bbox": [52, 17, 258, 48], "type": "text", "content": "落地案例-街道客户"}]}], "index": 0, "level": 1}, {"bbox": [45, 84, 552, 154], "type": "text", "lines": [{"bbox": [45, 84, 552, 154], "spans": [{"bbox": [45, 84, 552, 154], "type": "text", "content": "广州市增城区新塘镇和永宁街道智慧反诈平台的试用从7月6日- 10月5日，通过客户部分统计数据可以看出，通过之前2个月的反诈平台的试用，新塘镇和永宁街道9月份警情数据同比环比数据相较于其他镇街降幅明显。"}]}], "index": 1}, {"bbox": [48, 158, 381, 199], "type": "text", "lines": [{"bbox": [48, 158, 381, 199], "spans": [{"bbox": [48, 158, 381, 199], "type": "text", "content": "·新塘镇9月份同比下降 "}, {"bbox": [48, 158, 381, 199], "type": "inline_equation", "content": "52.8\\%"}, {"bbox": [48, 158, 381, 199], "type": "text", "content": " ，环比下降 "}, {"bbox": [48, 158, 381, 199], "type": "inline_equation", "content": "38\\%"}, {"bbox": [48, 158, 381, 199], "type": "text", "content": " ·永宁街道9月份同比下降 "}, {"bbox": [48, 158, 381, 199], "type": "inline_equation", "content": "68.6\\%"}, {"bbox": [48, 158, 381, 199], "type": "text", "content": " ，环比下降 "}, {"bbox": [48, 158, 381, 199], "type": "inline_equation", "content": "44.8\\%"}, {"bbox": [48, 158, 381, 199], "type": "text", "content": " ·"}]}], "index": 2}, {"bbox": [48, 223, 554, 264], "type": "text", "lines": [{"bbox": [48, 223, 554, 264], "spans": [{"bbox": [48, 223, 554, 264], "type": "text", "content": "·新塘镇9月份反诈平台共计发送涉诈闪信提醒71390人次，其中49358人提供后未再访问，劝阻成功率 "}, {"bbox": [48, 223, 554, 264], "type": "inline_equation", "content": "69.14\\%"}, {"bbox": [48, 223, 554, 264], "type": "text", "content": " 。"}]}], "index": 3}, {"bbox": [48, 267, 556, 307], "type": "text", "lines": [{"bbox": [48, 267, 556, 307], "spans": [{"bbox": [48, 267, 556, 307], "type": "text", "content": "·永宁街道9月份反诈平台共计发送涉诈闪信提醒32488人次，其中24814人提供后未再访问，劝阻成功率 "}, {"bbox": [48, 267, 556, 307], "type": "inline_equation", "content": "76.38\\%"}, {"bbox": [48, 267, 556, 307], "type": "text", "content": " 。"}]}], "index": 4}, {"bbox": [14, 328, 570, 349], "type": "text", "lines": [{"bbox": [14, 328, 570, 349], "spans": [{"bbox": [14, 328, 570, 349], "type": "text", "content": "- 广州市增城区新塘镇人民政府反诈态势数据报告 \n-广州市增城区永宁街道办事处反诈态势数据报告"}]}], "index": 5}, {"type": "table", "bbox": [16, 401, 280, 462], "blocks": [{"bbox": [16, 401, 280, 462], "lines": [{"bbox": [16, 401, 280, 462], "spans": [{"bbox": [16, 401, 280, 462], "type": "table", "html": "<table><tr><td>9个\n疑似欺诈号码</td><td>1256173个\n涉诈网站</td><td>103864个\n易受诈人数</td><td>71390个\n提醒人数</td></tr><tr><td>较上月-14</td><td>较上月+328019</td><td>较上月-27110</td><td>较上月-6626</td></tr></table>", "image_path": "4780b4bbbd506db97714aae7d6690ef0ed7897f6747d760156bb147e30810e88.jpg"}]}], "index": 7, "type": "table_body"}, {"bbox": [61, 366, 238, 384], "lines": [{"bbox": [61, 366, 238, 384], "spans": [{"bbox": [61, 366, 238, 384], "type": "text", "content": "2023年09月反诈态势数据统计"}]}], "index": 6, "type": "table_caption"}], "index": 7}, {"bbox": [18, 474, 264, 528], "type": "text", "lines": [{"bbox": [18, 474, 264, 528], "spans": [{"bbox": [18, 474, 264, 528], "type": "text", "content": "·本月共1248644个涉诈网站设置了反诈提醒业务，共提醒了71390人，其中49358人提醒后当日未再访问涉诈网站，劝阻成功率达到 "}, {"bbox": [18, 474, 264, 528], "type": "inline_equation", "content": "69.14\\%"}]}], "index": 8}, {"type": "table", "bbox": [299, 400, 559, 462], "blocks": [{"bbox": [299, 400, 559, 462], "lines": [{"bbox": [299, 400, 559, 462], "spans": [{"bbox": [299, 400, 559, 462], "type": "table", "html": "<table><tr><td>0个\n疑似欺诈号码</td><td>1256173个\n涉诈网站</td><td>46926个\n易受诈人数</td><td>32488个\n提醒人数</td></tr><tr><td>较上月0</td><td>较上月+328019</td><td>较上月-17252</td><td>较上月-3329</td></tr></table>", "image_path": "66fe0e03236126c739f38b5f212add3e22c3c3e6e567de8d52629920a6c9f298.jpg"}]}], "index": 10, "type": "table_body"}, {"bbox": [343, 366, 523, 384], "lines": [{"bbox": [343, 366, 523, 384], "spans": [{"bbox": [343, 366, 523, 384], "type": "text", "content": "#2023年09月反诈态势数据统计"}]}], "index": 9, "type": "table_caption"}], "index": 10}, {"bbox": [299, 472, 552, 528], "type": "text", "lines": [{"bbox": [299, 472, 552, 528], "spans": [{"bbox": [299, 472, 552, 528], "type": "text", "content": "·本月共1248644个涉诈网站设置了反诈提醒业务，共提醒了32488人，其中24814人提醒后当日未再访问涉诈网站，劝阻成功率达到 "}, {"bbox": [299, 472, 552, 528], "type": "inline_equation", "content": "76.38\\%"}]}], "index": 11}, {"type": "table", "bbox": [587, 54, 941, 534], "blocks": [{"bbox": [587, 54, 941, 534], "lines": [{"bbox": [587, 54, 941, 534], "spans": [{"bbox": [587, 54, 941, 534], "type": "table", "html": "<table><tr><td rowspan=\"2\">镇街</td><td rowspan=\"2\">派出所</td><td colspan=\"3\">9月份</td><td colspan=\"2\">今年以来</td></tr><tr><td>警情</td><td>同比</td><td>环比</td><td>警情</td><td>同比</td></tr><tr><td></td><td>全区</td><td>191</td><td>-51.8%</td><td>-24.5%</td><td>3218</td><td>-0.1%</td></tr><tr><td rowspan=\"8\">新塘镇</td><td>卫山</td><td>26</td><td>-23.5%</td><td>4.0%</td><td>271</td><td>-13.7%</td></tr><tr><td>沙村</td><td>17</td><td>-70.2%</td><td>-55.3%</td><td>377</td><td>-3.3%</td></tr><tr><td>大敦</td><td>9</td><td>-35.7%</td><td>-47.1%</td><td>186</td><td>12.7%</td></tr><tr><td>甘东</td><td>10</td><td>-33.3%</td><td>0.0%</td><td>119</td><td>-0.8%</td></tr><tr><td>西宁</td><td>2</td><td>-84.6%</td><td>-81.8%</td><td>96</td><td>-20.0%</td></tr><tr><td>白石</td><td>7</td><td>-58.8%</td><td>0.0%</td><td>124</td><td>-22.5%</td></tr><tr><td>沙埔</td><td>4</td><td>-55.6%</td><td>-69.2%</td><td>111</td><td>131.3%</td></tr><tr><td>合计</td><td>75</td><td>-52.8%</td><td>-38.0%</td><td>1284</td><td>-2.5%</td></tr><tr><td rowspan=\"3\">荔城街</td><td>富鹏</td><td>17</td><td>-46.9%</td><td>0.0%</td><td>228</td><td>-7.7%</td></tr><tr><td>荔城</td><td>8</td><td>-33.3%</td><td>-52.9%</td><td>136</td><td>-10.5%</td></tr><tr><td>合计</td><td>25</td><td>-43.2%</td><td>-26.5%</td><td>364</td><td>-8.8%</td></tr><tr><td rowspan=\"3\">永宁街</td><td>永宁</td><td>6</td><td>-81.3%</td><td>-60.0%</td><td>198</td><td>-12.8%</td></tr><tr><td>永新</td><td>10</td><td>-47.4%</td><td>-28.6%</td><td>181</td><td>-8.1%</td></tr><tr><td>合计</td><td>16</td><td>-68.6%</td><td>-44.8%</td><td>379</td><td>-10.6%</td></tr><tr><td>增江街</td><td>增江</td><td>15</td><td>-44.4%</td><td>114.3%</td><td>178</td><td>-9.2%</td></tr><tr><td rowspan=\"3\">中新镇</td><td>中新</td><td>9</td><td>-55.0%</td><td>-10.0%</td><td>129</td><td>30.3%</td></tr><tr><td>福和</td><td>4</td><td>-33.3%</td><td>100.0%</td><td>53</td><td>39.5%</td></tr><tr><td>合计</td><td>13</td><td>-50.0%</td><td>8.3%</td><td>182</td><td>32.8%</td></tr><tr><td>朱村街</td><td>朱村</td><td>6</td><td>-62.5%</td><td>-25.0%</td><td>144</td><td>69.4%</td></tr><tr><td rowspan=\"3\">宁西街</td><td>南香山</td><td>10</td><td>-33.3%</td><td>11.1%</td><td>160</td><td>1.3%</td></tr><tr><td>宁西</td><td>3</td><td>-40.0%</td><td>300.0%</td><td>25</td><td>-51.9%</td></tr><tr><td>合计</td><td>13</td><td>-35.0%</td><td>44.4%</td><td>185</td><td>-11.9%</td></tr><tr><td rowspan=\"3\">石滩镇</td><td>石滩</td><td>7</td><td>-46.2%</td><td>-36.4%</td><td>150</td><td>-15.3%</td></tr><tr><td>三江</td><td>3</td><td>-50.0%</td><td>0.0%</td><td>57</td><td>9.6%</td></tr><tr><td>合计</td><td>10</td><td>-47.4%</td><td>-28.6%</td><td>207</td><td>-9.6%</td></tr><tr><td>派潭镇</td><td>派潭</td><td>3</td><td>0.0%</td><td>50.0%</td><td>40</td><td>-20.0%</td></tr><tr><td>仙村镇</td><td>仙村</td><td>5</td><td>-16.7%</td><td>150.0%</td><td>69</td><td>1.5%</td></tr><tr><td>正果镇</td><td>正果</td><td>2</td><td>-16.0%</td><td>200.0%</td><td>13</td><td>-35.0%</td></tr><tr><td>荔湖街</td><td>雁塔</td><td>7</td><td>-72.0%</td><td>-50.0%</td><td>157</td><td>41.4%</td></tr><tr><td>小楼镇</td><td>小楼</td><td>1</td><td>100.0%</td><td>0.0%</td><td>16</td><td>-40.7%</td></tr></table>", "image_path": "01b92495168557ebd1a1f5352f370b3935e976866e10b86bbe22c261d1a45bb3.jpg"}]}], "index": 12, "type": "table_body"}], "index": 12}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 27}, {"para_blocks": [{"bbox": [52, 18, 258, 48], "type": "title", "lines": [{"bbox": [52, 18, 258, 48], "spans": [{"bbox": [52, 18, 258, 48], "type": "text", "content": "商洽案例-高校客户"}]}], "index": 0, "level": 1}, {"bbox": [45, 74, 926, 171], "type": "text", "lines": [{"bbox": [45, 74, 926, 171], "spans": [{"bbox": [45, 74, 926, 171], "type": "text", "content": "珠海科技学院（原吉林大学珠海学院）成立于2004年5月18日，全校师生共有3万多人，为全面提升高校“无诈校园”综合治理效能，学校保卫处结合当前形势任务，以安全防范为重点，以确保学校安全稳定为目标。坚持“以防为主、打防结合”和“专群结合、群防群治”的工作方针，积极开展校园反诈治理工作，预防和减少校园电信网络诈骗事件的发生，保护师生的人身财产安全，维护学校正常的教学、科研工作和生活秩序，努力创造安全文明的校园环境。"}]}], "index": 1}, {"bbox": [45, 176, 926, 247], "type": "text", "lines": [{"bbox": [45, 176, 926, 247], "spans": [{"bbox": [45, 176, 926, 247], "type": "text", "content": "2024年1月份，广东移动基于智慧反诈平台同珠海科技学院展开线下交流，针对高校学生网络电信诈骗案件的防范与事前预警，智慧反诈平台针对全校师生提供广东省范围内师生反诈宣防、涉诈预警功能，维护学校治安、保障师生财产安全、提高“无诈校园”治理能力，保障学校的安全稳定和公共秩序。"}]}], "index": 2}, {"bbox": [74, 270, 223, 292], "type": "title", "lines": [{"bbox": [74, 270, 223, 292], "spans": [{"bbox": [74, 270, 223, 292], "type": "text", "content": "高校涉诈类型分析"}]}], "index": 3, "level": 1}, {"bbox": [50, 303, 108, 320], "type": "title", "lines": [{"bbox": [50, 303, 108, 320], "spans": [{"bbox": [50, 303, 108, 320], "type": "text", "content": "网络刷单"}]}], "index": 4, "level": 1}, {"bbox": [354, 303, 413, 320], "type": "title", "lines": [{"bbox": [354, 303, 413, 320], "spans": [{"bbox": [354, 303, 413, 320], "type": "text", "content": "虚假投资"}]}], "index": 5, "level": 1}, {"bbox": [657, 303, 714, 320], "type": "title", "lines": [{"bbox": [657, 303, 714, 320], "spans": [{"bbox": [657, 303, 714, 320], "type": "text", "content": "游戏交易"}]}], "index": 6, "level": 1}, {"bbox": [50, 328, 312, 385], "type": "text", "lines": [{"bbox": [50, 328, 312, 385], "spans": [{"bbox": [50, 328, 312, 385], "type": "text", "content": "- 网上交易刷单，通过做任务返利模式进行诈骗；访问网络刷单网站、涉诈APP，反诈平台实时发送闪信劝阻提醒，减低涉诈风险；"}]}], "index": 7}, {"bbox": [352, 328, 616, 385], "type": "text", "lines": [{"bbox": [352, 328, 616, 385], "spans": [{"bbox": [352, 328, 616, 385], "type": "text", "content": "- 以高利集资为诱饵，使他人上当受骗。访问虚假投资、私盘集资网站、涉诈APP，反诈平台实时发送闪信劝阻提醒，减低涉诈风险；"}]}], "index": 8}, {"bbox": [657, 328, 917, 385], "type": "text", "lines": [{"bbox": [657, 328, 917, 385], "spans": [{"bbox": [657, 328, 917, 385], "type": "text", "content": "- 低价出售装备、账号，先付款，再给账号；访问非正规交易平台、涉诈APP，反诈平台实时发送闪信劝阻提醒，减低涉诈风险；"}]}], "index": 9}, {"bbox": [50, 414, 108, 434], "type": "title", "lines": [{"bbox": [50, 414, 108, 434], "spans": [{"bbox": [50, 414, 108, 434], "type": "text", "content": "网上借贷"}]}], "index": 10, "level": 1}, {"bbox": [354, 414, 413, 428], "type": "title", "lines": [{"bbox": [354, 414, 413, 428], "spans": [{"bbox": [354, 414, 413, 428], "type": "text", "content": "冒充客服"}]}], "index": 11, "level": 1}, {"bbox": [657, 414, 714, 429], "type": "title", "lines": [{"bbox": [657, 414, 714, 429], "spans": [{"bbox": [657, 414, 714, 429], "type": "text", "content": "冒充熟人"}]}], "index": 12, "level": 1}, {"bbox": [50, 442, 312, 500], "type": "text", "lines": [{"bbox": [50, 442, 312, 500], "spans": [{"bbox": [50, 442, 312, 500], "type": "text", "content": "- 以低利息低要求为诱饵，使他人上当受骗。访问虚假贷款、钓鱼网站、涉诈APP，反诈平台实时发送闪信劝阻提醒，减低涉诈风险；"}]}], "index": 13}, {"bbox": [352, 442, 602, 499], "type": "text", "lines": [{"bbox": [352, 442, 602, 499], "spans": [{"bbox": [352, 442, 602, 499], "type": "text", "content": "- 冒充网上购物客服，以订单异常进行退款诈骗；涉诈号码呼入，反诈平台实时发送闪信劝阻提醒，减低涉诈风险；"}]}], "index": 14}, {"bbox": [657, 442, 904, 499], "type": "text", "lines": [{"bbox": [657, 442, 904, 499], "spans": [{"bbox": [657, 442, 904, 499], "type": "text", "content": "- 非法获取学生信息，冒充熟人进行诈骗；涉诈号码呼入，反诈平台实时发送闪信劝阻提醒，减低涉诈风险；"}]}], "index": 15}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 28}, {"para_blocks": [{"bbox": [216, 235, 725, 298], "type": "title", "lines": [{"bbox": [216, 235, 725, 298], "spans": [{"bbox": [216, 235, 725, 298], "type": "text", "content": "感谢聆听 期待合作！"}]}], "index": 0, "level": 1}], "discarded_blocks": [], "page_size": [960, 540], "page_idx": 29}], "_backend": "vlm", "_version_name": "2.1.9"}